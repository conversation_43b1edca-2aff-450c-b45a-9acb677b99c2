const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const path = require('path');
require('dotenv').config();

const LoveConnectDB = require('../database/api');

const app = express();
const PORT = process.env.PORT || 5000;
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../build')));

// Database instance
const db = new LoveConnectDB();

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Auth Routes
app.post('/api/register', async (req, res) => {
  try {
    const { name, email, password, age, country, bio } = req.body;

    // Validation
    if (!name || !email || !password || !age || !country) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters' });
    }

    // Check if user already exists
    const existingUser = db.db.prepare('SELECT * FROM users WHERE email = ?').get(email);
    if (existingUser) {
      return res.status(400).json({ error: 'User already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const result = db.db.prepare(`
      INSERT INTO users (name, email, password, age, country, bio, verified, premium, distance)
      VALUES (?, ?, ?, ?, ?, ?, 0, 0, 0)
    `).run(name, email, hashedPassword, age, country, bio || '');

    const userId = result.lastInsertRowid;

    // Generate JWT token
    const token = jwt.sign({ userId, email }, JWT_SECRET, { expiresIn: '7d' });

    // Get user data (without password)
    const user = db.getUserById(userId);
    delete user.password;

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    // Find user
    const user = db.db.prepare('SELECT * FROM users WHERE email = ?').get(email);
    if (!user) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign({ userId: user.id, email: user.email }, JWT_SECRET, { expiresIn: '7d' });

    // Remove password from response
    delete user.password;

    res.json({
      message: 'Login successful',
      token,
      user
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Profile Routes
app.get('/api/profile', authenticateToken, (req, res) => {
  try {
    const user = db.getUserById(req.user.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    delete user.password;
    const photos = db.getUserPhotos(user.id);
    
    res.json({
      user,
      photos
    });

  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/api/profile', authenticateToken, (req, res) => {
  try {
    const { name, age, bio, country } = req.body;
    const userId = req.user.userId;

    const updateData = {};
    if (name) updateData.name = name;
    if (age) updateData.age = age;
    if (bio) updateData.bio = bio;
    if (country) updateData.country = country;

    const updatedUser = db.updateUser(userId, updateData);
    delete updatedUser.password;

    res.json({
      message: 'Profile updated successfully',
      user: updatedUser
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Users Routes (for dating app)
app.get('/api/users', authenticateToken, (req, res) => {
  try {
    const { country, minAge, maxAge, maxDistance, verified, premium, limit = 50 } = req.query;
    
    const filters = {
      country,
      minAge: minAge ? parseInt(minAge) : undefined,
      maxAge: maxAge ? parseInt(maxAge) : undefined,
      maxDistance: maxDistance ? parseInt(maxDistance) : undefined,
      verified: verified === 'true',
      premium: premium === 'true',
      limit: parseInt(limit)
    };

    // Exclude current user
    let users = db.searchUsers(filters);
    users = users.filter(user => user.id !== req.user.userId);

    // Get photos for each user
    const usersWithPhotos = users.map(user => {
      const photos = db.getUserPhotos(user.id);
      delete user.password;
      return { ...user, photos };
    });

    res.json(usersWithPhotos);

  } catch (error) {
    console.error('Users fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Likes Routes
app.post('/api/like', authenticateToken, (req, res) => {
  try {
    const { likedUserId, isSuper = false } = req.body;
    const likerId = req.user.userId;

    if (!likedUserId) {
      return res.status(400).json({ error: 'Liked user ID is required' });
    }

    // Add like
    db.addLike(likerId, likedUserId, isSuper ? 1 : 0);

    // Check for mutual like
    const isMutual = db.checkMutualLike(likerId, likedUserId);
    let match = null;

    if (isMutual) {
      // Create match
      const matchResult = db.createMatch(likerId, likedUserId);
      match = { id: matchResult.lastInsertRowid };
    }

    res.json({
      message: 'Like added successfully',
      isMutual,
      match
    });

  } catch (error) {
    console.error('Like error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Gifts Routes
app.get('/api/gifts', (req, res) => {
  try {
    const gifts = db.getAllGifts();
    res.json(gifts);
  } catch (error) {
    console.error('Gifts fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/send-gift', authenticateToken, (req, res) => {
  try {
    const { receiverId, giftId, message } = req.body;
    const senderId = req.user.userId;

    if (!receiverId || !giftId) {
      return res.status(400).json({ error: 'Receiver ID and Gift ID are required' });
    }

    const result = db.sendGift(senderId, receiverId, giftId, message);

    res.json({
      message: 'Gift sent successfully',
      transactionId: result.lastInsertRowid
    });

  } catch (error) {
    console.error('Send gift error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Matches Routes
app.get('/api/matches', authenticateToken, (req, res) => {
  try {
    const userId = req.user.userId;

    const matches = db.db.prepare(`
      SELECT
        m.*,
        CASE
          WHEN m.user1_id = ? THEN u2.name
          ELSE u1.name
        END as other_user_name,
        CASE
          WHEN m.user1_id = ? THEN m.user2_id
          ELSE m.user1_id
        END as other_user_id,
        msg.message_text as last_message,
        msg.sent_at as last_message_time
      FROM matches m
      JOIN users u1 ON m.user1_id = u1.id
      JOIN users u2 ON m.user2_id = u2.id
      LEFT JOIN (
        SELECT match_id, message_text, sent_at,
               ROW_NUMBER() OVER (PARTITION BY match_id ORDER BY sent_at DESC) as rn
        FROM messages
      ) msg ON m.id = msg.match_id AND msg.rn = 1
      WHERE m.user1_id = ? OR m.user2_id = ?
      ORDER BY COALESCE(msg.sent_at, m.matched_at) DESC
    `).all(userId, userId, userId, userId);

    res.json(matches);
  } catch (error) {
    console.error('Matches fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Messages Routes
app.get('/api/messages/:matchId', authenticateToken, (req, res) => {
  try {
    const { matchId } = req.params;
    const userId = req.user.userId;

    // Verify user is part of this match
    const match = db.db.prepare(`
      SELECT * FROM matches
      WHERE id = ? AND (user1_id = ? OR user2_id = ?)
    `).get(matchId, userId, userId);

    if (!match) {
      return res.status(403).json({ error: 'Access denied to this match' });
    }

    const messages = db.db.prepare(`
      SELECT
        msg.*,
        u.name as sender_name
      FROM messages msg
      JOIN users u ON msg.sender_id = u.id
      WHERE msg.match_id = ?
      ORDER BY msg.sent_at ASC
    `).all(matchId);

    res.json(messages);
  } catch (error) {
    console.error('Messages fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/messages', authenticateToken, (req, res) => {
  try {
    const { matchId, content } = req.body;
    const userId = req.user.userId;

    if (!matchId || !content || !content.trim()) {
      return res.status(400).json({ error: 'Match ID and message content are required' });
    }

    // Verify user is part of this match
    const match = db.db.prepare(`
      SELECT * FROM matches
      WHERE id = ? AND (user1_id = ? OR user2_id = ?)
    `).get(matchId, userId, userId);

    if (!match) {
      return res.status(403).json({ error: 'Access denied to this match' });
    }

    const result = db.db.prepare(`
      INSERT INTO messages (match_id, sender_id, message_text)
      VALUES (?, ?, ?)
    `).run(matchId, userId, content.trim());

    res.json({
      message: 'Message sent successfully',
      messageId: result.lastInsertRowid
    });
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Transactions Routes
app.get('/api/transactions', authenticateToken, (req, res) => {
  try {
    const userId = req.user.userId;

    const transactions = db.db.prepare(`
      SELECT
        gt.*,
        g.name as gift_name,
        g.icon as gift_icon,
        g.price_czk as gift_price_czk,
        sender.name as sender_name,
        receiver.name as receiver_name
      FROM gift_transactions gt
      JOIN gifts g ON gt.gift_id = g.id
      JOIN users sender ON gt.sender_id = sender.id
      JOIN users receiver ON gt.receiver_id = receiver.id
      WHERE gt.sender_id = ? OR gt.receiver_id = ?
      ORDER BY gt.created_at DESC
    `).all(userId, userId);

    res.json(transactions);
  } catch (error) {
    console.error('Transactions fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Serve React app for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../build/index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 LoveConnect server running on port ${PORT}`);
  console.log(`📍 API endpoints available at http://localhost:${PORT}/api`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  db.close();
  process.exit(0);
});
