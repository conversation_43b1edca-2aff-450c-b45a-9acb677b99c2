{"name": "DWG Extractor", "nodes": [{"parameters": {"functionCode": "// Input data should contain file information\nconst fileData = $input.item.json;\nconst binaryData = $input.item.binary;\n\n// Ensure we have the necessary data\nif (!fileData || !binaryData || !binaryData.data) {\n  throw new Error('Missing file data');\n}\n\n// Extract data from DWG\nconst extractedData = await extractDataFromDWG(binaryData.data);\n\n// Parse extracted data to identify elements, dimensions, etc.\nconst metadata = parseExtractedData(extractedData);\n\n// Create extracted data record\nconst extractedDataRecord = {\n  fileId: fileData.fileId,\n  projectId: fileData.projectId,\n  fileType: 'dwg',\n  extractionDate: new Date().toISOString(),\n  rawData: JSON.stringify(extractedData),\n  metadata: metadata\n};\n\n// Save extracted data to database\nconst savedData = await saveExtractedData(extractedDataRecord);\n\n// Return the result\nreturn {\n  ...fileData,\n  extractedData: savedData.data,\n  extractedDataId: savedData.data._id\n};\n\n// Helper function to extract data from DWG\nasync function extractDataFromDWG(dwgBuffer) {\n  // In a real implementation, this would use a DWG parsing library\n  // For this template, we'll simulate extraction\n  return {\n    entities: [\n      { type: 'LINE', count: 245, length: 1250 },\n      { type: 'CIRCLE', count: 32, radius: [0.5, 2.5] },\n      { type: 'TEXT', count: 78, content: ['Room 101', 'Kitchen', 'Bathroom', 'Bedroom'] },\n      { type: 'POLYLINE', count: 56, length: 850 },\n      { type: 'DIMENSION', count: 42, values: [3.5, 4.2, 2.8, 5.0] }\n    ],\n    layers: [\n      { name: 'Walls', entities: 120 },\n      { name: 'Doors', entities: 24 },\n      { name: 'Windows', entities: 36 },\n      { name: 'Furniture', entities: 48 },\n      { name: 'Dimensions', entities: 42 },\n      { name: 'Text', entities: 78 }\n    ],\n    dimensions: {\n      width: 25.5,\n      length: 18.2,\n      height: 3.0,\n      unit: 'm'\n    }\n  };\n}\n\n// Helper function to parse extracted data\nfunction parseExtractedData(data) {\n  // In a real implementation, this would analyze the DWG entities\n  // For this template, we'll simulate parsing\n  \n  const elements = [\n    { type: 'Stěna', count: 42, length: 320, thickness: 0.3 },\n    { type: 'Okno', count: 18, dimensions: '1.5x1.2' },\n    { type: 'Dveře', count: 24, dimensions: '0.9x2.0' },\n    { type: 'Sloup', count: 8, dimensions: '0.3x0.3' }\n  ];\n  \n  const rooms = [\n    { name: 'Obývací pokoj', area: 32.5, perimeter: 23.0 },\n    { name: 'Ložnice', area: 18.2, perimeter: 17.0 },\n    { name: 'Kuchyně', area: 12.8, perimeter: 14.4 },\n    { name: 'Koupelna', area: 6.5, perimeter: 10.2 }\n  ];\n  \n  const materials = [\n    { name: 'Zdivo nosné', quantity: 125, unit: 'm2' },\n    { name: 'Zdivo příčky', quantity: 85, unit: 'm2' },\n    { name: 'Beton základy', quantity: 45, unit: 'm3' },\n    { name: 'Beton stropy', quantity: 95, unit: 'm3' }\n  ];\n  \n  return {\n    projectName: 'Bytový dům - půdorys',\n    elements,\n    rooms,\n    materials,\n    dimensions: data.dimensions\n  };\n}\n\n// Helper function to save extracted data\nasync function saveExtractedData(data) {\n  // In a real implementation, this would call the API to save the data\n  // For this template, we'll simulate API call\n  \n  try {\n    const response = await $http.post({\n      url: `${$env.BUILDING_BUDGET_API_URL}/api/extracted-data`,\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${$env.BUILDING_BUDGET_API_KEY}`\n      },\n      body: data\n    });\n    \n    return response;\n  } catch (error) {\n    console.error('Error saving extracted data:', error);\n    // For demo purposes, return simulated response\n    return {\n      success: true,\n      data: {\n        ...data,\n        _id: `dwg_${Date.now()}`\n      }\n    };\n  }\n}"}, "name": "Extract DWG Data", "type": "n8n-nodes-base.function", "position": [250, 300]}, {"parameters": {"content": "={{ $json }}"}, "name": "Return Extracted Data", "type": "n8n-nodes-base.respond", "position": [450, 300]}], "connections": {"Extract DWG Data": {"main": [[{"node": "Return Extracted Data", "type": "main", "index": 0}]]}}}