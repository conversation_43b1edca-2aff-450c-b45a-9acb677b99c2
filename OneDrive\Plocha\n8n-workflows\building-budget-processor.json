{"name": "Building Budget Processor", "nodes": [{"parameters": {"httpMethod": "POST", "path": "building-budget", "options": {"binaryData": true, "responseMode": "lastNode"}}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [250, 300]}, {"parameters": {"mode": "expression", "expression": "={{ $json.binary ? $json.binary.mimeType : $json.fileType }}"}, "name": "Determine File Type", "type": "n8n-nodes-base.function", "position": [450, 300]}, {"parameters": {"mode": "rules", "rules": {"rules": [{"operation": "contains", "value": "application/pdf", "output": 0}, {"operation": "contains", "value": "dwg", "output": 1}, {"operation": "contains", "value": "ifc", "output": 2}]}}, "name": "Route by File Type", "type": "n8n-nodes-base.switch", "position": [650, 300]}, {"parameters": {"workflowId": "PDF Extractor"}, "name": "Process PDF", "type": "n8n-nodes-base.executeWorkflow", "position": [850, 200]}, {"parameters": {"workflowId": "DWG Extractor"}, "name": "Process DWG", "type": "n8n-nodes-base.executeWorkflow", "position": [850, 300]}, {"parameters": {"workflowId": "IFC Extractor"}, "name": "Process IFC", "type": "n8n-nodes-base.executeWorkflow", "position": [850, 400]}, {"parameters": {"workflowId": "Budget Generator"}, "name": "Generate Budget", "type": "n8n-nodes-base.executeWorkflow", "position": [1050, 300]}, {"parameters": {"url": "={{ $env.BUILDING_BUDGET_API_URL }}/api/files/processing-status", "method": "POST", "sendBody": true, "bodyParameters": {"parameters": [{"name": "fileId", "value": "={{ $json.fileId }}"}, {"name": "status", "value": "completed"}, {"name": "extractedDataId", "value": "={{ $json.extractedDataId }}"}]}, "options": {}}, "name": "Update Processing Status", "type": "n8n-nodes-base.httpRequest", "position": [1250, 300]}, {"parameters": {"content": "={\n  \"success\": true,\n  \"message\": \"File processed successfully\",\n  \"data\": $json\n}", "options": {}}, "name": "Respond with Success", "type": "n8n-nodes-base.respondToWebhook", "position": [1450, 300]}], "connections": {"Webhook": {"main": [[{"node": "Determine File Type", "type": "main", "index": 0}]]}, "Determine File Type": {"main": [[{"node": "Route by File Type", "type": "main", "index": 0}]]}, "Route by File Type": {"main": [[{"node": "Process PDF", "type": "main", "index": 0}], [{"node": "Process DWG", "type": "main", "index": 0}], [{"node": "Process IFC", "type": "main", "index": 0}]]}, "Process PDF": {"main": [[{"node": "Generate Budget", "type": "main", "index": 0}]]}, "Process DWG": {"main": [[{"node": "Generate Budget", "type": "main", "index": 0}]]}, "Process IFC": {"main": [[{"node": "Generate Budget", "type": "main", "index": 0}]]}, "Generate Budget": {"main": [[{"node": "Update Processing Status", "type": "main", "index": 0}]]}, "Update Processing Status": {"main": [[{"node": "Respond with Success", "type": "main", "index": 0}]]}}}