{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}], [\"path\", {\n  d: \"M12 2v4\",\n  key: \"3427ic\"\n}], [\"path\", {\n  d: \"m6.8 15-3.5 2\",\n  key: \"hjy98k\"\n}], [\"path\", {\n  d: \"m20.7 7-3.5 2\",\n  key: \"f08gto\"\n}], [\"path\", {\n  d: \"M6.8 9 3.3 7\",\n  key: \"1aevh4\"\n}], [\"path\", {\n  d: \"m20.7 17-3.5-2\",\n  key: \"1liqo3\"\n}], [\"path\", {\n  d: \"m9 22 3-8 3 8\",\n  key: \"wees03\"\n}], [\"path\", {\n  d: \"M8 22h8\",\n  key: \"rmew8v\"\n}], [\"path\", {\n  d: \"M18 18.7a9 9 0 1 0-12 0\",\n  key: \"dhzg4g\"\n}]];\nconst FerrisWheel = createLucideIcon(\"ferris-wheel\", __iconNode);\nexport { __iconNode, FerrisWheel as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "FerrisWheel", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Plocha\\dating-app\\my-dating-app\\node_modules\\lucide-react\\src\\icons\\ferris-wheel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '2', key: '1c9p78' }],\n  ['path', { d: 'M12 2v4', key: '3427ic' }],\n  ['path', { d: 'm6.8 15-3.5 2', key: 'hjy98k' }],\n  ['path', { d: 'm20.7 7-3.5 2', key: 'f08gto' }],\n  ['path', { d: 'M6.8 9 3.3 7', key: '1aevh4' }],\n  ['path', { d: 'm20.7 17-3.5-2', key: '1liqo3' }],\n  ['path', { d: 'm9 22 3-8 3 8', key: 'wees03' }],\n  ['path', { d: 'M8 22h8', key: 'rmew8v' }],\n  ['path', { d: 'M18 18.7a9 9 0 1 0-12 0', key: 'dhzg4g' }],\n];\n\n/**\n * @component @name FerrisWheel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIyIiAvPgogIDxwYXRoIGQ9Ik0xMiAydjQiIC8+CiAgPHBhdGggZD0ibTYuOCAxNS0zLjUgMiIgLz4KICA8cGF0aCBkPSJtMjAuNyA3LTMuNSAyIiAvPgogIDxwYXRoIGQ9Ik02LjggOSAzLjMgNyIgLz4KICA8cGF0aCBkPSJtMjAuNyAxNy0zLjUtMiIgLz4KICA8cGF0aCBkPSJtOSAyMiAzLTggMyA4IiAvPgogIDxwYXRoIGQ9Ik04IDIyaDgiIC8+CiAgPHBhdGggZD0iTTE4IDE4LjdhOSA5IDAgMSAwLTEyIDAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/ferris-wheel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FerrisWheel = createLucideIcon('ferris-wheel', __iconNode);\n\nexport default FerrisWheel;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAU,GAC1D;AAaM,MAAAE,WAAA,GAAcC,gBAAiB,iBAAgBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}