{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"rect\", {\n  x: \"15\",\n  y: \"5\",\n  width: \"4\",\n  height: \"12\",\n  rx: \"1\",\n  key: \"q8uenq\"\n}], [\"rect\", {\n  x: \"7\",\n  y: \"8\",\n  width: \"4\",\n  height: \"9\",\n  rx: \"1\",\n  key: \"sr5ea\"\n}]];\nconst ChartColumnBig = createLucideIcon(\"chart-column-big\", __iconNode);\nexport { __iconNode, ChartColumnBig as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "ChartColumnBig", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Plocha\\dating-app\\my-dating-app\\node_modules\\lucide-react\\src\\icons\\chart-column-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 3v16a2 2 0 0 0 2 2h16', key: 'c24i48' }],\n  ['rect', { x: '15', y: '5', width: '4', height: '12', rx: '1', key: 'q8uenq' }],\n  ['rect', { x: '7', y: '8', width: '4', height: '9', rx: '1', key: 'sr5ea' }],\n];\n\n/**\n * @component @name ChartColumnBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE2YTIgMiAwIDAgMCAyIDJoMTYiIC8+CiAgPHJlY3QgeD0iMTUiIHk9IjUiIHdpZHRoPSI0IiBoZWlnaHQ9IjEyIiByeD0iMSIgLz4KICA8cmVjdCB4PSI3IiB5PSI4IiB3aWR0aD0iNCIgaGVpZ2h0PSI5IiByeD0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chart-column-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChartColumnBig = createLucideIcon('chart-column-big', __iconNode);\n\nexport default ChartColumnBig;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAMC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAS,GAC7E;AAaM,MAAAM,cAAA,GAAiBC,gBAAiB,qBAAoBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}