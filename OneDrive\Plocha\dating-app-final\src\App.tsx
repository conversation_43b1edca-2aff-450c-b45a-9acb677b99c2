import React, { useState, useEffect } from 'react';
import './App.css';

const DatingApp = () => {
  const [currentProfile, setCurrentProfile] = useState(0);
  const [showGifts, setShowGifts] = useState(false);
  const [country, setCountry] = useState('cz');
  const [matches, setMatches] = useState<any[]>([]);
  const [showNotification, setShowNotification] = useState<string | false>(false);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    ageRange: [18, 35],
    maxDistance: 50,
    verified: false,
    premium: false
  });
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [showMenu, setShowMenu] = useState(false);

  // PWA Install Prompt
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: any) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setShowInstallPrompt(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;

    if (outcome === 'accepted') {
      setShowInstallPrompt(false);
    }

    setDeferredPrompt(null);
  };

  const countries = {
    cz: { name: 'Česko', currency: 'Kč', flag: '🇨🇿' },
    sk: { name: 'Slovensko', currency: 'EUR', flag: '🇸🇰' },
    de: { name: 'Deutschland', currency: 'EUR', flag: '🇩🇪' },
    it: { name: 'Italia', currency: 'EUR', flag: '🇮🇹' }
  };

  const gifts = [
    { id: 1, name: 'Růže', icon: '🌹', price: { cz: 10, other: 0.5 }, category: 'romantic' },
    { id: 2, name: 'Čtyřlístek', icon: '🍀', price: { cz: 15, other: 0.6 }, category: 'lucky' },
    { id: 3, name: 'Srdce', icon: '💖', price: { cz: 30, other: 1.2 }, category: 'romantic' },
    { id: 4, name: 'Šampáňo', icon: '🥂', price: { cz: 50, other: 2.0 }, category: 'luxury' },
    { id: 5, name: 'Čokoláda', icon: '🍫', price: { cz: 25, other: 1.0 }, category: 'sweet' },
    { id: 6, name: 'Diamant', icon: '💎', price: { cz: 200, other: 8 }, category: 'luxury' },
    { id: 7, name: 'Koruna', icon: '👑', price: { cz: 150, other: 6 }, category: 'royal' },
    { id: 8, name: 'Ferrari', icon: '🏎️', price: { cz: 1000, other: 40 }, category: 'ultimate' },
    { id: 9, name: 'Jednorožec', icon: '🦄', price: { cz: 80, other: 3.2 }, category: 'magical' },
    { id: 10, name: 'Hvězda', icon: '⭐', price: { cz: 35, other: 1.4 }, category: 'special' },
    { id: 11, name: 'Motýl', icon: '🦋', price: { cz: 20, other: 0.8 }, category: 'nature' },
    { id: 12, name: 'Zámek', icon: '🏰', price: { cz: 500, other: 20 }, category: 'ultimate' }
  ];

  const profiles = [
    {
      id: 1,
      name: 'Tereza',
      age: 24,
      location: 'Praha',
      country: 'cz',
      distance: 5,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Miluji cestování a dobrou kávu ☕ Hledám někoho pro sdílení zážitků. Život je příliš krátký na nudu! 🌟',
      interests: ['☕ Káva', '✈️ Cestování', '📚 Čtení', '🎵 Hudba'],
      job: 'Marketing Manager'
    },
    {
      id: 2,
      name: 'Klára',
      age: 26,
      location: 'Brno',
      country: 'cz',
      distance: 12,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=600&fit=crop&auto=format&q=80&sat=-20&con=10',
        'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=600&fit=crop&auto=format&q=80&brightness=10'
      ],
      bio: 'Fotografka a milovnice přírody 📸 Hledám partnera na dobrodružství!',
      interests: ['📸 Fotografie', '🌲 Příroda', '🥾 Hiking', '🎨 Umění'],
      job: 'Fotografka'
    },
    {
      id: 3,
      name: 'Anička',
      age: 23,
      location: 'Ostrava',
      country: 'cz',
      distance: 8,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1506863530036-1efeddceb993?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Studentka medicíny 👩‍⚕️ Ráda sportuji a poznávám nové lidi. Hledám někoho upřímného!',
      interests: ['🏃‍♀️ Sport', '📖 Studium', '🎬 Filmy', '🍕 Jídlo'],
      job: 'Studentka medicíny'
    },
    {
      id: 4,
      name: 'Veronika',
      age: 27,
      location: 'České Budějovice',
      country: 'cz',
      distance: 18,
      verified: false,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1499952127939-9bbf5af6c51c?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1502823403499-6ccfcf4fb453?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Učitelka na základní škole 👩‍🏫 Miluji děti, knihy a dlouhé procházky. Hledám někoho laskavého.',
      interests: ['📚 Knihy', '🚶‍♀️ Procházky', '🎨 Kreslení', '🐕 Psi'],
      job: 'Učitelka'
    },
    {
      id: 5,
      name: 'Nikola',
      age: 25,
      location: 'Plzeň',
      country: 'cz',
      distance: 22,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1524250502761-1ac6f2e30d43?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1524250502761-1ac6f2e30d43?w=400&h=600&fit=crop&auto=format&q=80&sat=20',
        'https://images.unsplash.com/photo-1524250502761-1ac6f2e30d43?w=400&h=600&fit=crop&auto=format&q=80&con=15'
      ],
      bio: 'Grafická designérka 🎨 Kreativní duše, která ráda objevuje nová místa a zažívá dobrodružství!',
      interests: ['🎨 Design', '✈️ Cestování', '📷 Fotografie', '🍷 Víno'],
      job: 'Grafická designérka'
    },
    {
      id: 6,
      name: 'Isabella',
      age: 28,
      location: 'Milano',
      country: 'it',
      distance: 15,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1619895862022-09114b41f16f?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1641149206308-0c0b98ba8056?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Artista e sognatrice 🎨 Milano è la mia ispirazione!',
      interests: ['🎨 Arte', '🍷 Vino', '🎭 Teatro', '📸 Fotografia'],
      job: 'Art Director'
    },
    {
      id: 7,
      name: 'Giulia',
      age: 26,
      location: 'Roma',
      country: 'it',
      distance: 25,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Architetta appassionata di storia e cultura 🏛️ Amo Roma e tutto il suo fascino!',
      interests: ['🏛️ Architettura', '📚 Storia', '🍝 Cucina', '🎵 Musica'],
      job: 'Architetto'
    },
    {
      id: 8,
      name: 'Emma',
      age: 26,
      location: 'Berlin',
      country: 'de',
      distance: 8,
      verified: false,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1521577352947-9bb58764b69a?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Berlin ist amazing! Love exploring new places and meeting interesting people 🌍',
      interests: ['🎵 Music', '🍺 Beer', '🚴 Cycling', '📱 Tech'],
      job: 'Software Developer'
    },
    {
      id: 9,
      name: 'Lena',
      age: 24,
      location: 'München',
      country: 'de',
      distance: 12,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Oktoberfest lover 🍺 Ich liebe Bayern, Berge und gutes Essen! Suche jemanden für Abenteuer.',
      interests: ['🏔️ Berge', '🍺 Bier', '🥨 Essen', '🎿 Skifahren'],
      job: 'Marketing Managerin'
    },
    {
      id: 10,
      name: 'Zuzana',
      age: 25,
      location: 'Bratislava',
      country: 'sk',
      distance: 6,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1521577352947-9bb58764b69a?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1521577352947-9bb58764b69a?w=400&h=600&fit=crop&auto=format&q=80&sat=10',
        'https://images.unsplash.com/photo-1521577352947-9bb58764b69a?w=400&h=600&fit=crop&auto=format&q=80&brightness=5'
      ],
      bio: 'Milujem Bratislavu a jej históriu 🏰 Hľadám niekoho, s kým môžem zdieľať svoje záľuby!',
      interests: ['🏰 História', '🎭 Divadlo', '📚 Čítanie', '🍷 Víno'],
      job: 'Historička'
    },
    // MUŽI
    {
      id: 11,
      name: 'Tomáš',
      age: 28,
      location: 'Praha',
      country: 'cz',
      distance: 3,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop&auto=format&q=80&sat=15',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop&auto=format&q=80&con=10'
      ],
      bio: 'IT konzultant a milovník technologií 💻 Rád cestuji, sportuji a objevujem nové věci!',
      interests: ['💻 Technologie', '✈️ Cestování', '🏃‍♂️ Běh', '🎮 Gaming'],
      job: 'IT Konzultant'
    },
    {
      id: 12,
      name: 'Jakub',
      age: 30,
      location: 'Brno',
      country: 'cz',
      distance: 15,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=600&fit=crop&auto=format&q=80&brightness=8',
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=600&fit=crop&auto=format&q=80&sat=12'
      ],
      bio: 'Architekt s láskou k designu 🏗️ Hledám někoho, kdo má rád umění a dobré jídlo!',
      interests: ['🏗️ Architektura', '🎨 Umění', '🍽️ Gastronomie', '🚴‍♂️ Cyklistika'],
      job: 'Architekt'
    },
    {
      id: 13,
      name: 'Petr',
      age: 32,
      location: 'Ostrava',
      country: 'cz',
      distance: 20,
      verified: false,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=600&fit=crop&auto=format&q=80&con=8',
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=600&fit=crop&auto=format&q=80&sat=5'
      ],
      bio: 'Učitel tělocviku a trenér 🏃‍♂️ Sport je můj život! Hledám aktivní partnerku.',
      interests: ['🏃‍♂️ Sport', '⚽ Fotbal', '🏋️‍♂️ Fitness', '🏔️ Hory'],
      job: 'Učitel a trenér'
    },
    {
      id: 14,
      name: 'Marco',
      age: 29,
      location: 'Milano',
      country: 'it',
      distance: 12,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=600&fit=crop&auto=format&q=80&sat=10',
        'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=600&fit=crop&auto=format&q=80&brightness=5'
      ],
      bio: 'Chef appassionato di cucina italiana 👨‍🍳 Amo cucinare, viaggiare e la bella vita!',
      interests: ['👨‍🍳 Cucina', '🍷 Vino', '✈️ Viaggi', '🎵 Musica'],
      job: 'Chef'
    },
    {
      id: 15,
      name: 'Alessandro',
      age: 31,
      location: 'Roma',
      country: 'it',
      distance: 18,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400&h=600&fit=crop&auto=format&q=80&con=12',
        'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400&h=600&fit=crop&auto=format&q=80&sat=8'
      ],
      bio: 'Fotografo e artista 📸 Roma è il mio studio all\'aperto! Cerco qualcuno che ami l\'arte.',
      interests: ['📸 Fotografia', '🎨 Arte', '🏛️ Storia', '🍝 Pasta'],
      job: 'Fotografo'
    },
    {
      id: 16,
      name: 'Max',
      age: 27,
      location: 'Berlin',
      country: 'de',
      distance: 10,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=600&fit=crop&auto=format&q=80&brightness=7',
        'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=600&fit=crop&auto=format&q=80&sat=15'
      ],
      bio: 'Startup founder und Tech-Enthusiast 🚀 Berlin ist meine Inspiration! Suche jemanden Ambitionierten.',
      interests: ['🚀 Startups', '💻 Tech', '🍺 Craft Beer', '🎧 Electronic Music'],
      job: 'Startup Founder'
    },
    {
      id: 17,
      name: 'Stefan',
      age: 33,
      location: 'München',
      country: 'de',
      distance: 25,
      verified: false,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?w=400&h=600&fit=crop&auto=format&q=80&con=10',
        'https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?w=400&h=600&fit=crop&auto=format&q=80&sat=5'
      ],
      bio: 'Ingenieur bei BMW 🚗 Liebe Autos, Berge und bayerische Gemütlichkeit! Suche echte Verbindung.',
      interests: ['🚗 Autos', '🏔️ Berge', '🍺 Bier', '🎿 Skifahren'],
      job: 'Ingenieur'
    },
    {
      id: 18,
      name: 'Martin',
      age: 29,
      location: 'Bratislava',
      country: 'sk',
      distance: 8,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1463453091185-61582044d556?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1463453091185-61582044d556?w=400&h=600&fit=crop&auto=format&q=80&brightness=8',
        'https://images.unsplash.com/photo-1463453091185-61582044d556?w=400&h=600&fit=crop&auto=format&q=80&sat=12'
      ],
      bio: 'Hudobník a producent 🎵 Hudba je môj život! Hľadám niekoho, kto má rád umenie a kultúru.',
      interests: ['🎵 Hudba', '🎸 Gitara', '🎭 Kultúra', '🍷 Víno'],
      job: 'Hudobník'
    },
    // DALŠÍ ŽENY
    {
      id: 19,
      name: 'Kateřina',
      age: 29,
      location: 'Praha',
      country: 'cz',
      distance: 7,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=400&h=600&fit=crop&auto=format&q=80&sat=10',
        'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=400&h=600&fit=crop&auto=format&q=80&brightness=5'
      ],
      bio: 'Právnička a milovnice kultury ⚖️ Ráda chodím do divadla, čtu knihy a objevuji nová místa!',
      interests: ['⚖️ Právo', '🎭 Divadlo', '📚 Knihy', '🍷 Víno'],
      job: 'Právnička'
    },
    {
      id: 20,
      name: 'Barbora',
      age: 24,
      location: 'Liberec',
      country: 'cz',
      distance: 14,
      verified: false,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=400&h=600&fit=crop&auto=format&q=80&con=8',
        'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=400&h=600&fit=crop&auto=format&q=80&sat=12'
      ],
      bio: 'Veterinářka a milovnice zvířat 🐕 Hledám někoho laskavého, kdo má rád přírodu a zvířata!',
      interests: ['🐕 Zvířata', '🌲 Příroda', '🚶‍♀️ Turistika', '📖 Čtení'],
      job: 'Veterinářka'
    },
    {
      id: 21,
      name: 'Chiara',
      age: 25,
      location: 'Firenze',
      country: 'it',
      distance: 20,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=600&fit=crop&auto=format&q=80&brightness=7',
        'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=600&fit=crop&auto=format&q=80&sat=15'
      ],
      bio: 'Insegnante di arte e storia 🎨 Firenze è la mia passione! Cerco qualcuno che ami la bellezza.',
      interests: ['🎨 Arte', '🏛️ Storia', '📚 Libri', '🍝 Cucina'],
      job: 'Insegnante'
    },
    {
      id: 22,
      name: 'Anna',
      age: 26,
      location: 'Hamburg',
      country: 'de',
      distance: 16,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1502823403499-6ccfcf4fb453?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1502823403499-6ccfcf4fb453?w=400&h=600&fit=crop&auto=format&q=80&con=10',
        'https://images.unsplash.com/photo-1502823403499-6ccfcf4fb453?w=400&h=600&fit=crop&auto=format&q=80&sat=8'
      ],
      bio: 'Meeresbiologin und Umweltschützerin 🌊 Hamburg und das Meer sind mein Zuhause!',
      interests: ['🌊 Meer', '🐟 Biologie', '🌱 Umwelt', '⛵ Segeln'],
      job: 'Meeresbiologin'
    },
    {
      id: 23,
      name: 'Lucia',
      age: 27,
      location: 'Košice',
      country: 'sk',
      distance: 11,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=400&h=600&fit=crop&auto=format&q=80&brightness=6',
        'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=400&h=600&fit=crop&auto=format&q=80&sat=10'
      ],
      bio: 'Lekárka a cestovateľka 👩‍⚕️ Milujem pomáhať ľuďom a objavovať nové kultúry!',
      interests: ['👩‍⚕️ Medicína', '✈️ Cestovanie', '📚 Vzdelávanie', '🏃‍♀️ Beh'],
      job: 'Lekárka'
    }
  ];

  const getFilteredProfiles = () => {
    return profiles.filter(profile => {
      // Country filter
      if (profile.country !== country) return false;

      // Age filter
      if (profile.age < filters.ageRange[0] || profile.age > filters.ageRange[1]) return false;

      // Distance filter
      if (profile.distance > filters.maxDistance) return false;

      // Verified filter
      if (filters.verified && !profile.verified) return false;

      // Premium filter
      if (filters.premium && !profile.premium) return false;

      return true;
    });
  };

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const message = {
      id: Date.now(),
      text: newMessage,
      sender: 'me',
      timestamp: new Date().toLocaleTimeString()
    };

    setChatMessages(prev => [...prev, message]);
    setNewMessage('');

    // Simulate response
    setTimeout(() => {
      const response = {
        id: Date.now() + 1,
        text: `Díky za zprávu! ${currentProfileData?.name} ti odpoví brzy 😊`,
        sender: currentProfileData?.name || 'Partner',
        timestamp: new Date().toLocaleTimeString()
      };
      setChatMessages(prev => [...prev, response]);
    }, 1000);
  };

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      handleSwipe('left');
    } else if (isRightSwipe) {
      handleSwipe('right');
    }
  };

  const filteredProfiles = getFilteredProfiles();
  const currentProfileData = filteredProfiles[currentProfile % filteredProfiles.length];

  const handleSwipe = (direction: 'left' | 'right') => {
    if (isAnimating) return; // Prevent multiple swipes during animation

    setIsAnimating(true);
    setSwipeDirection(direction);

    // Animate out
    setTimeout(() => {
      if (direction === 'right' && Math.random() > 0.6 && currentProfileData) {
        setMatches(prev => [...prev.filter(m => m.id !== currentProfileData.id), currentProfileData]);
        setShowNotification(`🎉 It's a Match! ${currentProfileData.name} ti také dala like!`);
        setTimeout(() => setShowNotification(false), 5000);
      }

      setCurrentProfile((prev) => (prev + 1) % filteredProfiles.length);
      setCurrentPhotoIndex(0); // Reset photo index for new profile

      // Reset animation state
      setTimeout(() => {
        setSwipeDirection(null);
        setIsAnimating(false);
      }, 100);
    }, 300);
  };

  const nextPhoto = () => {
    setCurrentPhotoIndex((prev) => (prev + 1) % currentProfileData.photos.length);
  };

  const getPrice = (gift: any) => {
    const price = country === 'cz' ? gift.price.cz : gift.price.other;
    const currency = countries[country as keyof typeof countries].currency;
    return `${price} ${currency}`;
  };

  if (!currentProfileData) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100vh', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <div style={{ textAlign: 'center', color: 'white' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>❤️</div>
          <p>Načítám profily...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      maxWidth: '400px',
      margin: '0 auto',
      minHeight: '100vh',
      background: isDarkMode
        ? 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)'
        : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
      transition: 'background 0.3s ease'
    }}>
      {/* Header */}
      <div style={{
        background: 'linear-gradient(135deg, #ff6b6b, #ee5a24)',
        color: 'white',
        padding: '20px',
        borderRadius: '20px 20px 0 0',
        textAlign: 'center',
        position: 'relative'
      }}>
        <select
          value={country}
          onChange={(e) => {
            setCountry(e.target.value);
            setCurrentProfile(0);
          }}
          style={{
            position: 'absolute',
            top: '15px',
            right: '15px',
            background: 'rgba(255,255,255,0.2)',
            border: 'none',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '10px',
            cursor: 'pointer'
          }}
        >
          {Object.entries(countries).map(([code, data]) => (
            <option key={code} value={code} style={{ color: 'black' }}>
              {data.flag} {data.name}
            </option>
          ))}
        </select>
        <button
          onClick={() => setShowFilters(true)}
          style={{
            position: 'absolute',
            top: '15px',
            left: '15px',
            background: 'rgba(255,255,255,0.2)',
            border: 'none',
            color: 'white',
            padding: '8px',
            borderRadius: '50%',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          🔍
        </button>
        <button
          onClick={() => setShowMenu(true)}
          style={{
            position: 'absolute',
            top: '15px',
            right: '15px',
            background: 'rgba(255,255,255,0.2)',
            border: 'none',
            color: 'white',
            padding: '8px',
            borderRadius: '50%',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ☰
        </button>
        <h1 style={{ margin: '0 0 5px 0', fontSize: '24px' }}>❤️ LoveConnect</h1>
        <p style={{ margin: 0, opacity: 0.9, fontSize: '14px' }}>Najdi svou spřízněnou duši</p>
      </div>

      {/* Notification */}
      {showNotification && (
        <div style={{
          position: 'fixed',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: 'linear-gradient(135deg, #00b894, #00cec9)',
          color: 'white',
          padding: '15px 25px',
          borderRadius: '10px',
          boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
          zIndex: 1000
        }}>
          {showNotification}
        </div>
      )}

      {/* Profile Card */}
      <div
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        style={{
          background: isDarkMode ? '#2d3748' : 'white',
          borderRadius: '0 0 20px 20px',
          boxShadow: isDarkMode
            ? '0 20px 40px rgba(0,0,0,0.3)'
            : '0 20px 40px rgba(0,0,0,0.1)',
          overflow: 'hidden',
          transform: swipeDirection === 'left' ? 'translateX(-100%) rotate(-10deg)' :
                     swipeDirection === 'right' ? 'translateX(100%) rotate(10deg)' : 'translateX(0) rotate(0)',
          transition: 'transform 0.3s ease-out, opacity 0.3s ease-out, background 0.3s ease',
          opacity: swipeDirection ? 0 : 1,
          color: isDarkMode ? 'white' : 'black',
          touchAction: 'pan-y' // Allow vertical scrolling but handle horizontal swipes
        }}>
        <div style={{ position: 'relative' }}>
          <img
            src={currentProfileData.photos[currentPhotoIndex]}
            alt={currentProfileData.name}
            style={{
              width: '100%',
              height: '400px',
              objectFit: 'cover',
              cursor: 'pointer'
            }}
            onClick={nextPhoto}
          />

          {/* Photo indicators */}
          <div style={{
            position: 'absolute',
            top: '15px',
            left: '15px',
            right: '15px',
            display: 'flex',
            gap: '5px'
          }}>
            {currentProfileData.photos.map((_, index) => (
              <div
                key={index}
                style={{
                  flex: 1,
                  height: '3px',
                  borderRadius: '2px',
                  background: index === currentPhotoIndex ? 'white' : 'rgba(255,255,255,0.4)',
                  transition: 'all 0.3s ease'
                }}
              />
            ))}
          </div>

          {/* Badges */}
          <div style={{
            position: 'absolute',
            top: '15px',
            right: '15px',
            display: 'flex',
            flexDirection: 'column',
            gap: '8px'
          }}>
            {currentProfileData.verified && (
              <div style={{
                background: '#3498db',
                borderRadius: '50%',
                padding: '8px',
                color: 'white',
                fontSize: '12px'
              }}>
                🛡️
              </div>
            )}
            {currentProfileData.premium && (
              <div style={{
                background: 'linear-gradient(135deg, #f39c12, #e67e22)',
                borderRadius: '50%',
                padding: '8px',
                color: 'white',
                fontSize: '12px'
              }}>
                ⭐
              </div>
            )}
          </div>

          {/* Distance */}
          <div style={{
            position: 'absolute',
            bottom: '15px',
            left: '15px',
            background: 'rgba(0,0,0,0.7)',
            color: 'white',
            padding: '8px 15px',
            borderRadius: '20px',
            fontSize: '12px'
          }}>
            📍 {currentProfileData.distance} km away
          </div>
        </div>

        {/* Profile Info */}
        <div style={{ padding: '20px' }}>
          <div style={{ marginBottom: '15px' }}>
            <h2 style={{
              margin: '0 0 5px 0',
              fontSize: '28px',
              display: 'flex',
              alignItems: 'center',
              gap: '10px'
            }}>
              {currentProfileData.name}, {currentProfileData.age}
              {currentProfileData.verified && <span style={{ fontSize: '16px' }}>🛡️</span>}
            </h2>
            <p style={{ margin: '0 0 5px 0', color: '#666', fontSize: '14px' }}>
              📍 {currentProfileData.location}
            </p>
            <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
              💼 {currentProfileData.job}
            </p>
          </div>

          <p style={{
            color: '#333',
            marginBottom: '20px',
            lineHeight: '1.5',
            fontSize: '14px'
          }}>
            {currentProfileData.bio}
          </p>

          {/* Interests */}
          <div style={{ marginBottom: '30px' }}>
            <h3 style={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: '#333',
              marginBottom: '10px'
            }}>
              Interests
            </h3>
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '8px'
            }}>
              {currentProfileData.interests.map((interest, index) => (
                <span
                  key={index}
                  style={{
                    background: 'linear-gradient(135deg, #ff9a9e, #fecfef)',
                    color: '#333',
                    padding: '8px 15px',
                    borderRadius: '20px',
                    fontSize: '12px',
                    fontWeight: '500'
                  }}
                >
                  {interest}
                </span>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '20px',
            paddingBottom: '20px'
          }}>
            <button
              onClick={() => handleSwipe('left')}
              style={{
                width: '60px',
                height: '60px',
                border: 'none',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #ff6b6b, #ee5a24)',
                color: 'white',
                fontSize: '24px',
                cursor: 'pointer',
                boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
                transition: 'transform 0.3s ease'
              }}
            >
              ✕
            </button>

            <button
              onClick={() => setShowGifts(true)}
              style={{
                width: '60px',
                height: '60px',
                border: 'none',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #ffeaa7, #fdcb6e)',
                color: 'white',
                fontSize: '24px',
                cursor: 'pointer',
                boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
                transition: 'transform 0.3s ease'
              }}
            >
              🎁
            </button>

            <button
              onClick={() => setShowChat(true)}
              style={{
                width: '60px',
                height: '60px',
                border: 'none',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                color: 'white',
                fontSize: '24px',
                cursor: 'pointer',
                boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
                transition: 'transform 0.3s ease'
              }}
            >
              💬
            </button>

            <button
              onClick={() => handleSwipe('right')}
              style={{
                width: '60px',
                height: '60px',
                border: 'none',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #4ecdc4, #44a08d)',
                color: 'white',
                fontSize: '24px',
                cursor: 'pointer',
                boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
                transition: 'transform 0.3s ease'
              }}
            >
              ❤️
            </button>
          </div>
        </div>
      </div>

      {/* Gift Modal */}
      {showGifts && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '30px',
            borderRadius: '20px',
            maxWidth: '300px',
            width: '90%',
            textAlign: 'center',
            position: 'relative'
          }}>
            <button
              onClick={() => setShowGifts(false)}
              style={{
                position: 'absolute',
                top: '15px',
                right: '15px',
                background: '#e9ecef',
                border: 'none',
                width: '30px',
                height: '30px',
                borderRadius: '50%',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              ✕
            </button>
            <h3 style={{ marginBottom: '20px' }}>Send a Gift</h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '10px',
              maxHeight: '300px',
              overflowY: 'auto'
            }}>
              {gifts.map((gift) => (
                <button
                  key={gift.id}
                  onClick={() => {
                    alert(`🎁 ${gift.name} odeslán!`);
                    setShowGifts(false);
                  }}
                  style={{
                    background: '#f8f9fa',
                    border: 'none',
                    padding: '12px',
                    borderRadius: '12px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    fontSize: '12px',
                    textAlign: 'center'
                  }}
                >
                  <div style={{ fontSize: '24px', marginBottom: '8px' }}>{gift.icon}</div>
                  <div style={{ fontWeight: 'bold', marginBottom: '4px', fontSize: '11px' }}>{gift.name}</div>
                  <div style={{ fontSize: '10px', color: '#666' }}>
                    {getPrice(gift)}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Chat Modal */}
      {showChat && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: isDarkMode ? '#2d3748' : 'white',
            borderRadius: '20px',
            width: '90%',
            maxWidth: '400px',
            height: '500px',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative'
          }}>
            {/* Chat Header */}
            <div style={{
              background: 'linear-gradient(135deg, #667eea, #764ba2)',
              color: 'white',
              padding: '15px',
              borderRadius: '20px 20px 0 0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                <img
                  src={currentProfileData.photos[0]}
                  alt={currentProfileData.name}
                  style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '50%',
                    objectFit: 'cover'
                  }}
                />
                <div>
                  <h3 style={{ margin: 0, fontSize: '16px' }}>{currentProfileData.name}</h3>
                  <p style={{ margin: 0, fontSize: '12px', opacity: 0.8 }}>Online</p>
                </div>
              </div>
              <button
                onClick={() => setShowChat(false)}
                style={{
                  background: 'rgba(255,255,255,0.2)',
                  border: 'none',
                  color: 'white',
                  width: '30px',
                  height: '30px',
                  borderRadius: '50%',
                  cursor: 'pointer',
                  fontSize: '16px'
                }}
              >
                ✕
              </button>
            </div>

            {/* Chat Messages */}
            <div style={{
              flex: 1,
              padding: '15px',
              overflowY: 'auto',
              background: isDarkMode ? '#1a202c' : '#f8f9fa'
            }}>
              {chatMessages.length === 0 ? (
                <div style={{ textAlign: 'center', color: isDarkMode ? '#a0aec0' : '#666', marginTop: '50px' }}>
                  <p>💬 Začni konverzaci!</p>
                  <p style={{ fontSize: '14px' }}>Napiš první zprávu {currentProfileData.name}</p>
                </div>
              ) : (
                chatMessages.map((message) => (
                  <div
                    key={message.id}
                    style={{
                      marginBottom: '10px',
                      display: 'flex',
                      justifyContent: message.sender === 'me' ? 'flex-end' : 'flex-start'
                    }}
                  >
                    <div style={{
                      background: message.sender === 'me'
                        ? 'linear-gradient(135deg, #667eea, #764ba2)'
                        : 'white',
                      color: message.sender === 'me' ? 'white' : '#333',
                      padding: '10px 15px',
                      borderRadius: '20px',
                      maxWidth: '70%',
                      boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
                    }}>
                      <p style={{ margin: 0, fontSize: '14px' }}>{message.text}</p>
                      <p style={{
                        margin: '5px 0 0 0',
                        fontSize: '10px',
                        opacity: 0.7
                      }}>
                        {message.timestamp}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Chat Input */}
            <div style={{
              padding: '15px',
              borderTop: `1px solid ${isDarkMode ? '#4a5568' : '#eee'}`,
              display: 'flex',
              gap: '10px',
              background: isDarkMode ? '#2d3748' : 'white'
            }}>
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && sendMessage()}
                placeholder="Napiš zprávu..."
                style={{
                  flex: 1,
                  padding: '10px 15px',
                  border: `1px solid ${isDarkMode ? '#4a5568' : '#ddd'}`,
                  borderRadius: '25px',
                  outline: 'none',
                  fontSize: '14px',
                  background: isDarkMode ? '#1a202c' : 'white',
                  color: isDarkMode ? 'white' : '#333'
                }}
              />
              <button
                onClick={sendMessage}
                style={{
                  background: 'linear-gradient(135deg, #667eea, #764ba2)',
                  border: 'none',
                  color: 'white',
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  cursor: 'pointer',
                  fontSize: '16px'
                }}
              >
                ➤
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Filters Modal */}
      {showFilters && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: isDarkMode ? '#2d3748' : 'white',
            borderRadius: '20px',
            padding: '30px',
            width: '90%',
            maxWidth: '400px',
            maxHeight: '80vh',
            overflowY: 'auto',
            position: 'relative',
            color: isDarkMode ? 'white' : '#333'
          }}>
            <button
              onClick={() => setShowFilters(false)}
              style={{
                position: 'absolute',
                top: '15px',
                right: '15px',
                background: isDarkMode ? '#4a5568' : '#e9ecef',
                border: 'none',
                width: '30px',
                height: '30px',
                borderRadius: '50%',
                cursor: 'pointer',
                fontSize: '16px',
                color: isDarkMode ? 'white' : '#333'
              }}
            >
              ✕
            </button>

            <h3 style={{ marginBottom: '25px', fontSize: '20px', color: isDarkMode ? 'white' : '#333' }}>🔍 Filtry</h3>

            {/* Age Range */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                Věk: {filters.ageRange[0]} - {filters.ageRange[1]} let
              </label>
              <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                <input
                  type="range"
                  min="18"
                  max="60"
                  value={filters.ageRange[0]}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    ageRange: [parseInt(e.target.value), prev.ageRange[1]]
                  }))}
                  style={{ flex: 1 }}
                />
                <input
                  type="range"
                  min="18"
                  max="60"
                  value={filters.ageRange[1]}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    ageRange: [prev.ageRange[0], parseInt(e.target.value)]
                  }))}
                  style={{ flex: 1 }}
                />
              </div>
            </div>

            {/* Distance */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                Maximální vzdálenost: {filters.maxDistance} km
              </label>
              <input
                type="range"
                min="1"
                max="100"
                value={filters.maxDistance}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  maxDistance: parseInt(e.target.value)
                }))}
                style={{ width: '100%' }}
              />
            </div>

            {/* Checkboxes */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{ display: 'flex', alignItems: 'center', marginBottom: '10px', cursor: 'pointer' }}>
                <input
                  type="checkbox"
                  checked={filters.verified}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    verified: e.target.checked
                  }))}
                  style={{ marginRight: '8px' }}
                />
                🛡️ Pouze ověřené profily
              </label>

              <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                <input
                  type="checkbox"
                  checked={filters.premium}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    premium: e.target.checked
                  }))}
                  style={{ marginRight: '8px' }}
                />
                ⭐ Pouze Premium uživatele
              </label>
            </div>

            <button
              onClick={() => setShowFilters(false)}
              style={{
                width: '100%',
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                color: 'white',
                border: 'none',
                padding: '12px',
                borderRadius: '10px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer'
              }}
            >
              Použít filtry
            </button>
          </div>
        </div>
      )}

      {/* Hamburger Menu */}
      {showMenu && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'flex-end',
          alignItems: 'flex-start',
          zIndex: 1000
        }}>
          <div style={{
            background: isDarkMode ? '#2d3748' : 'white',
            width: '280px',
            height: '100%',
            padding: '20px',
            boxShadow: '-5px 0 15px rgba(0,0,0,0.2)',
            color: isDarkMode ? 'white' : '#333'
          }}>
            {/* Menu Header */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '30px',
              paddingBottom: '15px',
              borderBottom: `1px solid ${isDarkMode ? '#4a5568' : '#e2e8f0'}`
            }}>
              <h3 style={{ margin: 0, fontSize: '20px' }}>Menu</h3>
              <button
                onClick={() => setShowMenu(false)}
                style={{
                  background: 'transparent',
                  border: 'none',
                  fontSize: '24px',
                  cursor: 'pointer',
                  color: isDarkMode ? 'white' : '#333'
                }}
              >
                ✕
              </button>
            </div>

            {/* Menu Items */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>

              {/* Dark Mode Toggle */}
              <div
                onClick={() => setIsDarkMode(!isDarkMode)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '15px',
                  padding: '15px',
                  borderRadius: '10px',
                  background: isDarkMode ? '#4a5568' : '#f7fafc',
                  cursor: 'pointer',
                  transition: 'background 0.2s ease'
                }}
              >
                <span style={{ fontSize: '20px' }}>{isDarkMode ? '☀️' : '🌙'}</span>
                <div>
                  <p style={{ margin: 0, fontWeight: 'bold' }}>
                    {isDarkMode ? 'Světlý režim' : 'Tmavý režim'}
                  </p>
                  <p style={{ margin: 0, fontSize: '12px', opacity: 0.7 }}>
                    Přepnout téma aplikace
                  </p>
                </div>
              </div>

              {/* Install App */}
              {showInstallPrompt && (
                <div
                  onClick={handleInstallClick}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '15px',
                    padding: '15px',
                    borderRadius: '10px',
                    background: isDarkMode ? '#4a5568' : '#f7fafc',
                    cursor: 'pointer',
                    transition: 'background 0.2s ease'
                  }}
                >
                  <span style={{ fontSize: '20px' }}>📱</span>
                  <div>
                    <p style={{ margin: 0, fontWeight: 'bold' }}>Nainstalovat aplikaci</p>
                    <p style={{ margin: 0, fontSize: '12px', opacity: 0.7 }}>
                      Přidat na plochu telefonu
                    </p>
                  </div>
                </div>
              )}

              {/* Matches */}
              <div
                onClick={() => {
                  setShowMenu(false);
                  // TODO: Show matches screen
                }}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '15px',
                  padding: '15px',
                  borderRadius: '10px',
                  background: isDarkMode ? '#4a5568' : '#f7fafc',
                  cursor: 'pointer',
                  transition: 'background 0.2s ease'
                }}
              >
                <span style={{ fontSize: '20px' }}>💕</span>
                <div>
                  <p style={{ margin: 0, fontWeight: 'bold' }}>Moje matche</p>
                  <p style={{ margin: 0, fontSize: '12px', opacity: 0.7 }}>
                    {matches.length} aktivních matchů
                  </p>
                </div>
              </div>

              {/* Profile Settings */}
              <div
                onClick={() => {
                  setShowMenu(false);
                  // TODO: Show profile settings
                }}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '15px',
                  padding: '15px',
                  borderRadius: '10px',
                  background: isDarkMode ? '#4a5568' : '#f7fafc',
                  cursor: 'pointer',
                  transition: 'background 0.2s ease'
                }}
              >
                <span style={{ fontSize: '20px' }}>👤</span>
                <div>
                  <p style={{ margin: 0, fontWeight: 'bold' }}>Můj profil</p>
                  <p style={{ margin: 0, fontSize: '12px', opacity: 0.7 }}>
                    Upravit profil a nastavení
                  </p>
                </div>
              </div>

              {/* Premium */}
              <div
                onClick={() => {
                  setShowMenu(false);
                  // TODO: Show premium screen
                }}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '15px',
                  padding: '15px',
                  borderRadius: '10px',
                  background: 'linear-gradient(135deg, #ffd700, #ffed4e)',
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease',
                  color: '#333'
                }}
              >
                <span style={{ fontSize: '20px' }}>⭐</span>
                <div>
                  <p style={{ margin: 0, fontWeight: 'bold' }}>LoveConnect Premium</p>
                  <p style={{ margin: 0, fontSize: '12px', opacity: 0.8 }}>
                    Získej více matchů
                  </p>
                </div>
              </div>

              {/* Help & Support */}
              <div
                onClick={() => {
                  setShowMenu(false);
                  // TODO: Show help screen
                }}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '15px',
                  padding: '15px',
                  borderRadius: '10px',
                  background: isDarkMode ? '#4a5568' : '#f7fafc',
                  cursor: 'pointer',
                  transition: 'background 0.2s ease'
                }}
              >
                <span style={{ fontSize: '20px' }}>❓</span>
                <div>
                  <p style={{ margin: 0, fontWeight: 'bold' }}>Nápověda</p>
                  <p style={{ margin: 0, fontSize: '12px', opacity: 0.7 }}>
                    Podpora a časté otázky
                  </p>
                </div>
              </div>

            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatingApp;
