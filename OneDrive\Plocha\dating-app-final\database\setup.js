const Database = require('better-sqlite3');
const path = require('path');

// Create database connection
const dbPath = path.join(__dirname, 'loveconnect.db');
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

console.log('🗄️ Setting up LoveConnect database...');

// Create tables
const createTables = () => {
  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      age INTEGER NOT NULL,
      country TEXT NOT NULL,
      bio TEXT,
      verified BOOLEAN DEFAULT 0,
      premium BOOLEAN DEFAULT 0,
      distance INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Photos table
  db.exec(`
    CREATE TABLE IF NOT EXISTS photos (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      photo_url TEXT NOT NULL,
      is_primary BOOLEAN DEFAULT 0,
      order_index INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )
  `);

  // Matches table
  db.exec(`
    CREATE TABLE IF NOT EXISTS matches (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user1_id INTEGER NOT NULL,
      user2_id INTEGER NOT NULL,
      matched_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      is_active BOOLEAN DEFAULT 1,
      FOREIGN KEY (user1_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (user2_id) REFERENCES users (id) ON DELETE CASCADE,
      UNIQUE(user1_id, user2_id)
    )
  `);

  // Messages table
  db.exec(`
    CREATE TABLE IF NOT EXISTS messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      match_id INTEGER NOT NULL,
      sender_id INTEGER NOT NULL,
      message_text TEXT NOT NULL,
      sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      read_at DATETIME,
      FOREIGN KEY (match_id) REFERENCES matches (id) ON DELETE CASCADE,
      FOREIGN KEY (sender_id) REFERENCES users (id) ON DELETE CASCADE
    )
  `);

  // Likes table
  db.exec(`
    CREATE TABLE IF NOT EXISTS likes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      liker_id INTEGER NOT NULL,
      liked_id INTEGER NOT NULL,
      is_super_like BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (liker_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (liked_id) REFERENCES users (id) ON DELETE CASCADE,
      UNIQUE(liker_id, liked_id)
    )
  `);

  // Gifts table
  db.exec(`
    CREATE TABLE IF NOT EXISTS gifts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      icon TEXT NOT NULL,
      price_czk INTEGER NOT NULL,
      price_eur INTEGER NOT NULL,
      price_usd INTEGER NOT NULL,
      category TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Gift transactions table
  db.exec(`
    CREATE TABLE IF NOT EXISTS gift_transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      sender_id INTEGER NOT NULL,
      receiver_id INTEGER NOT NULL,
      gift_id INTEGER NOT NULL,
      message TEXT,
      sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (sender_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (receiver_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (gift_id) REFERENCES gifts (id) ON DELETE CASCADE
    )
  `);

  console.log('✅ Tables created successfully');
};

// Insert sample data
const insertSampleData = () => {
  console.log('📝 Inserting sample data...');

  // Insert gifts
  const insertGift = db.prepare(`
    INSERT OR REPLACE INTO gifts (id, name, icon, price_czk, price_eur, price_usd, category)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);

  const gifts = [
    [1, 'Růže', '🌹', 10, 1, 1, 'romantic'],
    [2, 'Čtyřlístek', '🍀', 15, 1, 1, 'lucky'],
    [3, 'Srdce', '💖', 30, 2, 2, 'romantic'],
    [4, 'Diamant', '💎', 100, 5, 5, 'luxury'],
    [5, 'Šampaňské', '🍾', 80, 4, 4, 'celebration'],
    [6, 'Čokoláda', '🍫', 25, 2, 2, 'sweet'],
    [7, 'Květina', '🌸', 20, 1, 1, 'romantic'],
    [8, 'Hvězda', '⭐', 50, 3, 3, 'special'],
    [9, 'Koruna', '👑', 200, 10, 10, 'luxury'],
    [10, 'Motýl', '🦋', 35, 2, 2, 'beautiful'],
    [11, 'Duha', '🌈', 45, 3, 3, 'magical'],
    [12, 'Jednorožec', '🦄', 150, 8, 8, 'fantasy']
  ];

  gifts.forEach(gift => insertGift.run(...gift));

  // Insert sample users (with bcrypt hashed passwords)
  const bcrypt = require('bcryptjs');
  const defaultPassword = bcrypt.hashSync('password123', 10);

  const insertUser = db.prepare(`
    INSERT OR REPLACE INTO users (id, name, email, password, age, country, bio, verified, premium, distance)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const users = [
    [1, 'Anna', '<EMAIL>', defaultPassword, 25, 'cz', 'Miluji cestování a dobré víno 🍷', 1, 0, 5],
    [2, 'Petra', '<EMAIL>', defaultPassword, 28, 'cz', 'Fotografka a milovnice přírody 📸', 1, 1, 12],
    [3, 'Klára', '<EMAIL>', defaultPassword, 23, 'cz', 'Studentka medicíny, ráda čtu knihy 📚', 0, 0, 8],
    [4, 'Tereza', '<EMAIL>', defaultPassword, 30, 'cz', 'Jogínka a zdravý životní styl 🧘‍♀️', 1, 1, 15],
    [5, 'Veronika', '<EMAIL>', defaultPassword, 26, 'cz', 'Umělkyně, kreslím a maluji 🎨', 0, 0, 20],
    [6, 'Markéta', '<EMAIL>', defaultPassword, 29, 'cz', 'Právnička s láskou k hudbě 🎵', 1, 0, 7],
    [7, 'Zuzana', '<EMAIL>', defaultPassword, 24, 'cz', 'Tanečnice a choreografka 💃', 1, 1, 18],
    [8, 'Nikola', '<EMAIL>', defaultPassword, 27, 'cz', 'Kuchařka a foodbloggerka 👩‍🍳', 0, 0, 10],
    [9, 'Barbora', '<EMAIL>', defaultPassword, 31, 'cz', 'Architektka, miluji design 🏗️', 1, 1, 25],
    [10, 'Kristýna', '<EMAIL>', defaultPassword, 22, 'cz', 'Sportovkyně, běhám maratony 🏃‍♀️', 0, 0, 14]
  ];

  users.forEach(user => insertUser.run(...user));

  // Insert sample photos
  const insertPhoto = db.prepare(`
    INSERT OR REPLACE INTO photos (user_id, photo_url, is_primary, order_index)
    VALUES (?, ?, ?, ?)
  `);

  // Sample photos for each user (using placeholder images)
  for (let userId = 1; userId <= 10; userId++) {
    for (let photoIndex = 0; photoIndex < 3; photoIndex++) {
      insertPhoto.run(
        userId,
        `https://picsum.photos/400/600?random=${userId * 10 + photoIndex}`,
        photoIndex === 0 ? 1 : 0,
        photoIndex
      );
    }
  }

  // Insert sample matches (Anna with other users)
  const insertMatch = db.prepare(`
    INSERT OR REPLACE INTO matches (id, user1_id, user2_id, matched_at)
    VALUES (?, ?, ?, ?)
  `);

  const matches = [
    [1, 1, 2, '2024-01-14 10:30:00'], // Anna + Petra
    [2, 1, 4, '2024-01-14 14:15:00'], // Anna + Tereza
    [3, 1, 6, '2024-01-13 16:45:00'], // Anna + Markéta
  ];

  matches.forEach(match => insertMatch.run(...match));

  // Insert sample messages
  const insertMessage = db.prepare(`
    INSERT OR REPLACE INTO messages (id, match_id, sender_id, content, created_at)
    VALUES (?, ?, ?, ?, ?)
  `);

  const messages = [
    [1, 1, 2, 'Ahoj Anno! Jak se máš? 😊', '2024-01-14 10:35:00'],
    [2, 1, 1, 'Ahoj Petro! Mám se skvěle, díky! A ty?', '2024-01-14 10:37:00'],
    [3, 1, 2, 'Také super! Máš chuť někdy na kávu? ☕', '2024-01-14 10:40:00'],
    [4, 1, 1, 'To zní skvěle! Kdy by ti to vyhovovalo?', '2024-01-14 10:42:00'],

    [5, 2, 4, 'Hej Anno! Viděla jsem tvůj profil a líbíš se mi 💕', '2024-01-14 14:20:00'],
    [6, 2, 1, 'Ahoj Terezo! Děkuji, ty také vypadáš skvěle!', '2024-01-14 14:25:00'],
    [7, 2, 4, 'Děkuji! Ráda bych tě poznala blíž 😊', '2024-01-14 14:30:00'],

    [8, 3, 6, 'Ahoj Anno! Máme match! 🎉', '2024-01-13 16:50:00'],
    [9, 3, 1, 'Ahoj Markéto! Ano, těším se na poznání!', '2024-01-13 17:00:00'],
  ];

  messages.forEach(message => insertMessage.run(...message));

  console.log('✅ Sample data inserted successfully');
};

// Create indexes for better performance
const createIndexes = () => {
  db.exec(`CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_users_country ON users(country)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_users_age ON users(age)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_photos_user_id ON photos(user_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_matches_users ON matches(user1_id, user2_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_messages_match_id ON messages(match_id)`);
  db.exec(`CREATE INDEX IF NOT EXISTS idx_likes_users ON likes(liker_id, liked_id)`);

  console.log('✅ Indexes created successfully');
};

// Run setup
try {
  createTables();
  insertSampleData();
  createIndexes();
  
  console.log('🎉 Database setup completed successfully!');
  console.log(`📍 Database location: ${dbPath}`);
  
  // Show some stats
  const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
  const photoCount = db.prepare('SELECT COUNT(*) as count FROM photos').get();
  const giftCount = db.prepare('SELECT COUNT(*) as count FROM gifts').get();
  
  console.log(`👥 Users: ${userCount.count}`);
  console.log(`📸 Photos: ${photoCount.count}`);
  console.log(`🎁 Gifts: ${giftCount.count}`);
  
} catch (error) {
  console.error('❌ Database setup failed:', error);
} finally {
  db.close();
}
