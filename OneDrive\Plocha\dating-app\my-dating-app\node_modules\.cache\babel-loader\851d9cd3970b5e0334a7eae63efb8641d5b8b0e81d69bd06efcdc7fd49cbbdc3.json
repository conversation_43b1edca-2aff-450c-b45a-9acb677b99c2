{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4exip2\"\n}], [\"line\", {\n  x1: \"21.17\",\n  x2: \"12\",\n  y1: \"8\",\n  y2: \"8\",\n  key: \"a0cw5f\"\n}], [\"line\", {\n  x1: \"3.95\",\n  x2: \"8.54\",\n  y1: \"6.06\",\n  y2: \"14\",\n  key: \"1kftof\"\n}], [\"line\", {\n  x1: \"10.88\",\n  x2: \"15.46\",\n  y1: \"21.94\",\n  y2: \"14\",\n  key: \"1ymyh8\"\n}]];\nconst Chrome = createLucideIcon(\"chrome\", __iconNode);\nexport { __iconNode, Chrome as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2", "Chrome", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Plocha\\dating-app\\my-dating-app\\node_modules\\lucide-react\\src\\icons\\chrome.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['circle', { cx: '12', cy: '12', r: '4', key: '4exip2' }],\n  ['line', { x1: '21.17', x2: '12', y1: '8', y2: '8', key: 'a0cw5f' }],\n  ['line', { x1: '3.95', x2: '8.54', y1: '6.06', y2: '14', key: '1kftof' }],\n  ['line', { x1: '10.88', x2: '15.46', y1: '21.94', y2: '14', key: '1ymyh8' }],\n];\n\n/**\n * @component @name Chrome\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI0IiAvPgogIDxsaW5lIHgxPSIyMS4xNyIgeDI9IjEyIiB5MT0iOCIgeTI9IjgiIC8+CiAgPGxpbmUgeDE9IjMuOTUiIHgyPSI4LjU0IiB5MT0iNi4wNiIgeTI9IjE0IiAvPgogIDxsaW5lIHgxPSIxMC44OCIgeDI9IjE1LjQ2IiB5MT0iMjEuOTQiIHkyPSIxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chrome\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n * @deprecated Brand icons have been deprecated and are due to be removed, please refer to https://github.com/lucide-icons/lucide/issues/670. We recommend using https://simpleicons.org/?q=chrome instead. This icon will be removed in v1.0\n */\nconst Chrome = createLucideIcon('chrome', __iconNode);\n\nexport default Chrome;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMC,GAAK;AAAA,CAAU,GACzD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,QAAQ;EAAEC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,QAAQ;EAAEC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAU,GAC7E;AAaM,MAAAK,MAAA,GAASC,gBAAiB,WAAUV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}