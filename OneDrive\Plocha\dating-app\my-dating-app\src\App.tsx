import React, { useState, useEffect } from 'react';
import { Heart, X, MessageCircle, User, Gift, ArrowLeft, MapPin, Star, Shield, Filter, Settings } from 'lucide-react';

const DatingApp = () => {
  const [currentProfile, setCurrentProfile] = useState(0);
  const [showGifts, setShowGifts] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [showRegistration, setShowRegistration] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState('discover');
  const [country, setCountry] = useState('cz');
  const [matches, setMatches] = useState([]);
  const [chats, setChats] = useState([
    {
      id: 1,
      user: {
        name: '<PERSON><PERSON><PERSON>',
        photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=600&fit=crop&auto=format&q=80',
        age: 24,
        verified: true
      },
      lastMessage: 'Ahoj! Jak se máš? 😊',
      timestamp: '2 min',
      unread: 2,
      online: true
    },
    {
      id: 2,
      user: {
        name: 'Klára',
        photo: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=600&fit=crop&auto=format&q=80',
        age: 26,
        verified: true,
        premium: true
      },
      lastMessage: 'Máš rád fotografie? Právě jsem byla na výstavě...',
      timestamp: '1h',
      unread: 0,
      online: false
    },
    {
      id: 3,
      user: {
        name: 'Isabella',
        photo: 'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=600&fit=crop&auto=format&q=80',
        age: 28,
        verified: true,
        premium: true
      },
      lastMessage: 'Ciao! Milano è bellissima oggi 🌟',
      timestamp: '3h',
      unread: 1,
      online: false
    },
    {
      id: 4,
      user: {
        name: 'Emma',
        photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop&auto=format&q=80',
        age: 26,
        verified: false
      },
      lastMessage: 'Berlin ist amazing! Where should we meet?',
      timestamp: '1d',
      unread: 0,
      online: false
    },
    {
      id: 5,
      user: {
        name: 'Sophie',
        photo: 'https://images.unsplash.com/photo-1524250502761-1ac6f2e30d43?w=400&h=600&fit=crop&auto=format&q=80',
        age: 25,
        verified: true
      },
      lastMessage: 'Thanks for the gift! 💖',
      timestamp: '2d',
      unread: 0,
      online: false
    }
  ]);
  const [showNotification, setShowNotification] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [chatHistory, setChatHistory] = useState({});
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [filters, setFilters] = useState({
    ageRange: [18, 35],
    maxDistance: 50,
    interests: [],
    education: [],
    jobType: [],
    verified: false,
    premium: false,
    online: false,
    hasPhotos: true,
    bodyType: [],
    height: [150, 200],
    smoking: [],
    drinking: [],
    religion: [],
    children: [],
    relationship: []
  });

  const countries = {
    cz: { name: 'Česko', currency: 'Kč', flag: '🇨🇿' },
    sk: { name: 'Slovensko', currency: 'EUR', flag: '🇸🇰' },
    de: { name: 'Deutschland', currency: 'EUR', flag: '🇩🇪' },
    it: { name: 'Italia', currency: 'EUR', flag: '🇮🇹' }
  };

  const gifts = [
    { id: 1, name: 'Růže', icon: '🌹', price: { cz: 10, other: 0.5 } },
    { id: 2, name: 'Srdce', icon: '💖', price: { cz: 30, other: 1.2 } },
    { id: 3, name: 'Diamant', icon: '💎', price: { cz: 200, other: 8 } },
    { id: 4, name: 'Ferrari', icon: '🏎️', price: { cz: 1000, other: 40 } }
  ];

  const profiles = [
    {
      id: 1,
      name: 'Tereza',
      age: 24,
      location: 'Praha',
      country: 'cz',
      distance: 5,
      verified: true,
      premium: false,
      photos: [
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Miluji cestování a dobrou kávu ☕ Hledám někoho pro sdílení zážitků. Život je příliš krátký na nudu! 🌟',
      interests: ['☕ Káva', '✈️ Cestování', '📚 Čtení', '🎵 Hudba'],
      job: 'Marketing Manager',
      responses: ['Ahoj! Díky za zprávu! 😊', 'Ráda se seznamuji s novými lidmi!', 'Pro další rozhovor se zaregistruj! 💕'],
      responseDelay: [2000, 3000, 1500]
    },
    {
      id: 2,
      name: 'Klára',
      age: 26,
      location: 'Brno',
      country: 'cz',
      distance: 12,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1499952127939-9bbf5af6c51c?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Fotografka a milovnice přírody 📸 Hledám partnera na dobrodružství!',
      interests: ['📸 Fotografie', '🌲 Příroda', '🥾 Hiking', '🎨 Umění'],
      job: 'Fotografka',
      responses: ['Čau! Super zpráva! 📷', 'Máš rád přírodu?', 'Zaregistruj se pro víc! 🌲'],
      responseDelay: [2200, 3800, 2500]
    },
    {
      id: 3,
      name: 'Isabella',
      age: 28,
      location: 'Milano',
      country: 'it',
      distance: 15,
      verified: true,
      premium: true,
      photos: [
        'https://images.unsplash.com/photo-1616683693504-3ea7e9ad6fec?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1619895862022-09114b41f16f?w=400&h=600&fit=crop&auto=format&q=80',
        'https://images.unsplash.com/photo-1641149206308-0c0b98ba8056?w=400&h=600&fit=crop&auto=format&q=80'
      ],
      bio: 'Artista e sognatrice 🎨 Milano è la mia ispirazione!',
      interests: ['🎨 Arte', '🍷 Vino', '🎭 Teatro', '📸 Fotografia'],
      job: 'Art Director',
      responses: ['Ciao bello! 🇮🇹', 'Mi piaci molto!', 'Registrati per chattare! 💖'],
      responseDelay: [2500, 4000, 2000]
    }
  ];

  const getFilteredProfiles = () => {
    return profiles.filter(profile => {
      // Country filter
      if (profile.country !== country) return false;

      // Age filter
      if (profile.age < filters.ageRange[0] || profile.age > filters.ageRange[1]) return false;

      // Distance filter
      if (profile.distance > filters.maxDistance) return false;

      // Verified filter
      if (filters.verified && !profile.verified) return false;

      // Premium filter
      if (filters.premium && !profile.premium) return false;

      // Interest filter
      if (filters.interests.length > 0) {
        const hasCommonInterest = profile.interests.some(interest =>
          filters.interests.some(filterInterest =>
            interest.toLowerCase().includes(filterInterest.toLowerCase())
          )
        );
        if (!hasCommonInterest) return false;
      }

      // Education filter
      if (filters.education.length > 0) {
        const hasEducation = filters.education.some(edu =>
          profile.education?.toLowerCase().includes(edu.toLowerCase())
        );
        if (!hasEducation) return false;
      }

      return true;
    });
  };

  const filteredProfiles = getFilteredProfiles();
  const currentProfileData = filteredProfiles[currentProfile % filteredProfiles.length];

  const handleSwipe = (direction) => {
    if (direction === 'right' && Math.random() > 0.6 && currentProfileData) {
      setMatches(prev => [...prev.filter(m => m.id !== currentProfileData.id), currentProfileData]);
      setShowNotification(`🎉 It's a Match! ${currentProfileData.name} ti také dala like!`);
      setTimeout(() => setShowNotification(false), 5000);
    }
    setCurrentProfile((prev) => (prev + 1) % filteredProfiles.length);
  };

  const handleMessage = () => {
    const profile = currentProfileData;
    const chatKey = `chat_${profile.id}`;
    const currentChat = chatHistory[chatKey] || { messages: [], count: 0 };

    if (currentChat.count < 3) {
      setIsTyping(true);
      setTimeout(() => {
        setIsTyping(false);
        const newMessage = {
          text: profile.responses[currentChat.count],
          timestamp: new Date().toLocaleTimeString(),
          sender: profile.name
        };
        const updatedChat = {
          messages: [...currentChat.messages, newMessage],
          count: currentChat.count + 1
        };
        setChatHistory(prev => ({ ...prev, [chatKey]: updatedChat }));
        if (updatedChat.count >= 3) {
          setTimeout(() => setShowRegistration(true), 1000);
        }
      }, profile.responseDelay[currentChat.count] || 2000);
    }
  };

  const nextPhoto = () => {
    setCurrentPhotoIndex((prev) => (prev + 1) % currentProfileData.photos.length);
  };

  const getPrice = (gift) => {
    const price = country === 'cz' ? gift.price.cz : gift.price.other;
    const currency = countries[country].currency;
    return `${price} ${currency}`;
  };

  useEffect(() => {
    setCurrentPhotoIndex(0);
    setCurrentProfile(0);
  }, [country]);

  if (!currentProfileData) {
    return (
      <div className={`flex items-center justify-center h-screen ${isDarkMode ? 'bg-gray-900' : 'bg-gradient-to-br from-pink-100 to-purple-100'}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <Heart className="w-8 h-8 text-white" />
          </div>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Načítám profily...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`max-w-md mx-auto min-h-screen relative overflow-hidden ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-600 text-white p-4 flex justify-between items-center shadow-2xl">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg">
            <Heart className="w-6 h-6 text-pink-500" />
          </div>
          <div>
            <h1 className="font-bold text-xl">LoveConnect</h1>
            <p className="text-xs text-white/80">Find your soulmate</p>
          </div>
        </div>
        <select
          value={country}
          onChange={(e) => setCountry(e.target.value)}
          className="bg-white/20 rounded-xl px-3 py-2 text-sm backdrop-blur-sm border-0 focus:outline-none"
        >
          {Object.entries(countries).map(([code, data]) => (
            <option key={code} value={code} className="text-black bg-white">
              {data.flag} {data.name}
            </option>
          ))}
        </select>
      </div>

      {/* Notification */}
      {showNotification && (
        <div className="fixed top-20 left-4 right-4 z-50">
          <div className="bg-gradient-to-r from-green-500 to-teal-500 text-white p-4 rounded-2xl shadow-2xl animate-bounce">
            <div className="flex items-center space-x-3">
              <Heart className="w-6 h-6 animate-pulse" />
              <span className="font-semibold text-sm">{showNotification}</span>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {activeTab === 'discover' && (
        <div className="relative p-4 pb-24">
          <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-100'} rounded-3xl shadow-2xl overflow-hidden border`}>
            <div className="relative">
              <img
                src={currentProfileData.photos[currentPhotoIndex]}
                alt={currentProfileData.name}
                className="w-full h-[500px] object-cover cursor-pointer"
                onClick={nextPhoto}
              />

              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent pointer-events-none" />

              <div className="absolute top-4 left-4 right-4 flex space-x-2">
                {currentProfileData.photos.map((_, index) => (
                  <div
                    key={index}
                    className={`flex-1 h-1 rounded-full transition-all duration-500 ${
                      index === currentPhotoIndex ? 'bg-white shadow-lg' : 'bg-white/40'
                    }`}
                  />
                ))}
              </div>

              <div className="absolute top-4 right-4 flex flex-col space-y-2">
                {currentProfileData.verified && (
                  <div className="bg-blue-500 rounded-full p-2 shadow-lg">
                    <Shield className="w-4 h-4 text-white" />
                  </div>
                )}
                {currentProfileData.premium && (
                  <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full p-2 shadow-lg">
                    <Star className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>

              <div className="absolute bottom-6 left-6 bg-black/70 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium">
                <div className="flex items-center space-x-1">
                  <MapPin className="w-4 h-4" />
                  <span>{currentProfileData.distance} km away</span>
                </div>
              </div>
            </div>

            <div className={`p-6 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
              <div className="mb-4">
                <div className="flex items-center space-x-2 mb-2">
                  <h2 className="text-3xl font-bold">{currentProfileData.name}</h2>
                  <span className={`text-2xl ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {currentProfileData.age}
                  </span>
                  {currentProfileData.verified && <Shield className="w-5 h-5 text-blue-500" />}
                </div>
                <div className={`flex items-center space-x-1 mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  <MapPin className="w-4 h-4" />
                  <span className="text-sm">{currentProfileData.location}</span>
                </div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  💼 {currentProfileData.job}
                </p>
              </div>

              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-6 leading-relaxed text-sm`}>
                {currentProfileData.bio}
              </p>

              <div className="mb-8">
                <h3 className={`text-sm font-bold ${isDarkMode ? 'text-gray-200' : 'text-gray-900'} mb-3`}>
                  Interests
                </h3>
                <div className="flex flex-wrap gap-2">
                  {currentProfileData.interests.map((interest, index) => (
                    <span
                      key={index}
                      className={`${
                        isDarkMode
                          ? 'bg-gray-700 text-gray-200 border-gray-600'
                          : 'bg-gradient-to-r from-pink-50 to-purple-50 text-gray-700 border-pink-200'
                      } px-3 py-2 rounded-full text-xs font-medium border`}
                    >
                      {interest}
                    </span>
                  ))}
                </div>
              </div>

              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => handleSwipe('left')}
                  className="w-16 h-16 bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-xl"
                >
                  <X className="w-8 h-8 text-white" />
                </button>

                <button
                  onClick={() => setShowGifts(true)}
                  className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-xl"
                >
                  <Gift className="w-8 h-8 text-white" />
                </button>

                <button
                  onClick={() => setShowChat(true)}
                  className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-xl"
                >
                  <MessageCircle className="w-8 h-8 text-white" />
                </button>

                <button
                  onClick={() => handleSwipe('right')}
                  className="w-16 h-16 bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-600 hover:to-red-600 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-xl"
                >
                  <Heart className="w-8 h-8 text-white" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bottom Navigation */}
      <div className={`fixed bottom-0 left-0 right-0 ${isDarkMode ? 'bg-gray-900 border-gray-700' : 'bg-white border-gray-200'} border-t`}>
        <div className="max-w-md mx-auto flex justify-around py-2">
          <button
            onClick={() => setActiveTab('discover')}
            className={`flex flex-col items-center py-2 px-4 rounded-lg transition-colors ${
              activeTab === 'discover'
                ? 'text-pink-500'
                : `${isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-600 hover:text-gray-800'}`
            }`}
          >
            <Heart className="w-6 h-6 mb-1" />
            <span className="text-xs font-medium">Discover</span>
          </button>

          <button
            onClick={() => setActiveTab('matches')}
            className={`flex flex-col items-center py-2 px-4 rounded-lg transition-colors relative ${
              activeTab === 'matches'
                ? 'text-pink-500'
                : `${isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-600 hover:text-gray-800'}`
            }`}
          >
            <Star className="w-6 h-6 mb-1" />
            <span className="text-xs font-medium">Matches</span>
            {matches.length > 0 && (
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white font-bold">{matches.length}</span>
              </div>
            )}
          </button>

          <button
            onClick={() => setActiveTab('chats')}
            className={`flex flex-col items-center py-2 px-4 rounded-lg transition-colors relative ${
              activeTab === 'chats'
                ? 'text-pink-500'
                : `${isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-600 hover:text-gray-800'}`
            }`}
          >
            <MessageCircle className="w-6 h-6 mb-1" />
            <span className="text-xs font-medium">Messages</span>
            {chats.filter(chat => chat.unread > 0).length > 0 && (
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white font-bold">
                  {chats.reduce((sum, chat) => sum + chat.unread, 0)}
                </span>
              </div>
            )}
          </button>

          <button
            onClick={() => setShowFilters(true)}
            className={`flex flex-col items-center py-2 px-4 rounded-lg transition-colors ${
              isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <Filter className="w-6 h-6 mb-1" />
            <span className="text-xs font-medium">Filters</span>
          </button>
        </div>
      </div>

      {/* Gift Modal */}
      {showGifts && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-3xl p-6 max-w-sm w-full shadow-2xl`}>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold">Send a Gift</h3>
              <button onClick={() => setShowGifts(false)} className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {gifts.map((gift) => (
                <button
                  key={gift.id}
                  onClick={() => {setShowGifts(false); setShowChat(true);}}
                  className={`${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-50 hover:bg-gray-100'} rounded-2xl p-4 text-center transition-all transform hover:scale-105`}
                >
                  <div className="text-4xl mb-3">{gift.icon}</div>
                  <div className="text-sm font-semibold mb-1">{gift.name}</div>
                  <div className={`text-xs ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'} rounded-full px-2 py-1`}>
                    {getPrice(gift)}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatingApp;
