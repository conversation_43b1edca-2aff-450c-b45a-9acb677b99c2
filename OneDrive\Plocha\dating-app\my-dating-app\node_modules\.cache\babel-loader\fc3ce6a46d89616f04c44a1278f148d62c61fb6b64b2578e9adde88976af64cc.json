{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n  key: \"tnqrlb\"\n}], [\"path\", {\n  d: \"m2.305 15.53.923-.382\",\n  key: \"yfp9st\"\n}], [\"path\", {\n  d: \"m3.228 12.852-.924-.383\",\n  key: \"bckynb\"\n}], [\"path\", {\n  d: \"M4.677 21.5a2 2 0 0 0 1.313.5H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v2.5\",\n  key: \"1yo3oz\"\n}], [\"path\", {\n  d: \"m4.852 11.228-.383-.923\",\n  key: \"1j88i9\"\n}], [\"path\", {\n  d: \"m4.852 16.772-.383.924\",\n  key: \"sag1dv\"\n}], [\"path\", {\n  d: \"m7.148 11.228.383-.923\",\n  key: \"rj39hk\"\n}], [\"path\", {\n  d: \"m7.53 17.696-.382-.924\",\n  key: \"1uu5cs\"\n}], [\"path\", {\n  d: \"m8.772 12.852.923-.383\",\n  key: \"13811l\"\n}], [\"path\", {\n  d: \"m8.772 15.148.923.383\",\n  key: \"z1a5l0\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"14\",\n  r: \"3\",\n  key: \"a1xfv6\"\n}]];\nconst FileCog = createLucideIcon(\"file-cog\", __iconNode);\nexport { __iconNode, FileCog as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "FileCog", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Plocha\\dating-app\\my-dating-app\\node_modules\\lucide-react\\src\\icons\\file-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'm2.305 15.53.923-.382', key: 'yfp9st' }],\n  ['path', { d: 'm3.228 12.852-.924-.383', key: 'bckynb' }],\n  [\n    'path',\n    {\n      d: 'M4.677 21.5a2 2 0 0 0 1.313.5H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v2.5',\n      key: '1yo3oz',\n    },\n  ],\n  ['path', { d: 'm4.852 11.228-.383-.923', key: '1j88i9' }],\n  ['path', { d: 'm4.852 16.772-.383.924', key: 'sag1dv' }],\n  ['path', { d: 'm7.148 11.228.383-.923', key: 'rj39hk' }],\n  ['path', { d: 'm7.53 17.696-.382-.924', key: '1uu5cs' }],\n  ['path', { d: 'm8.772 12.852.923-.383', key: '13811l' }],\n  ['path', { d: 'm8.772 15.148.923.383', key: 'z1a5l0' }],\n  ['circle', { cx: '6', cy: '14', r: '3', key: 'a1xfv6' }],\n];\n\n/**\n * @component @name FileCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMnY0YTIgMiAwIDAgMCAyIDJoNCIgLz4KICA8cGF0aCBkPSJtMi4zMDUgMTUuNTMuOTIzLS4zODIiIC8+CiAgPHBhdGggZD0ibTMuMjI4IDEyLjg1Mi0uOTI0LS4zODMiIC8+CiAgPHBhdGggZD0iTTQuNjc3IDIxLjVhMiAyIDAgMCAwIDEuMzEzLjVIMThhMiAyIDAgMCAwIDItMlY3bC01LTVINmEyIDIgMCAwIDAtMiAydjIuNSIgLz4KICA8cGF0aCBkPSJtNC44NTIgMTEuMjI4LS4zODMtLjkyMyIgLz4KICA8cGF0aCBkPSJtNC44NTIgMTYuNzcyLS4zODMuOTI0IiAvPgogIDxwYXRoIGQ9Im03LjE0OCAxMS4yMjguMzgzLS45MjMiIC8+CiAgPHBhdGggZD0ibTcuNTMgMTcuNjk2LS4zODItLjkyNCIgLz4KICA8cGF0aCBkPSJtOC43NzIgMTIuODUyLjkyMy0uMzgzIiAvPgogIDxwYXRoIGQ9Im04Ljc3MiAxNS4xNDguOTIzLjM4MyIgLz4KICA8Y2lyY2xlIGN4PSI2IiBjeT0iMTQiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileCog = createLucideIcon('file-cog', __iconNode);\n\nexport default FileCog;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GACzD;AAaM,MAAAI,OAAA,GAAUC,gBAAiB,aAAYP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}