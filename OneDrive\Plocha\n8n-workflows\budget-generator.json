{"name": "Budget Generator", "nodes": [{"parameters": {"functionCode": "// Input data should contain extracted data information\nconst inputData = $input.item.json;\n\n// Ensure we have the necessary data\nif (!inputData || !inputData.extractedData) {\n  throw new Error('Missing extracted data');\n}\n\n// Get price data from database\nconst priceData = await getPriceData();\n\n// Generate budget based on extracted data\nconst budget = generateBudget(inputData.extractedData, priceData);\n\n// Create budget record\nconst budgetRecord = {\n  projectId: inputData.projectId,\n  fileId: inputData.fileId,\n  extractedDataId: inputData.extractedDataId,\n  name: `R<PERSON>počet - ${inputData.extractedData.metadata.projectName || 'Projekt'}`,\n  creationDate: new Date().toISOString(),\n  totalPrice: budget.totalPrice,\n  currency: 'CZK',\n  items: budget.items,\n  metadata: {\n    generatedBy: 'n8n',\n    fileType: inputData.extractedData.fileType,\n    priceDataVersion: priceData.version\n  }\n};\n\n// Save budget to database\nconst savedBudget = await saveBudget(budgetRecord);\n\n// Generate PDF version of the budget\nconst pdfUrl = await generateBudgetPDF(savedBudget.data);\n\n// Return the result\nreturn {\n  ...inputData,\n  budget: savedBudget.data,\n  budgetId: savedBudget.data._id,\n  pdfUrl: pdfUrl\n};\n\n// Helper function to get price data\nasync function getPriceData() {\n  // In a real implementation, this would call the API to get price data\n  // For this template, we'll use simulated price data\n  \n  return {\n    version: '2025.1',\n    lastUpdated: '2025-01-15',\n    materials: {\n      'Beton': { price: 3200, unit: 'm3' },\n      'Cihly': { price: 25, unit: 'ks' },\n      'Ocel': { price: 28500, unit: 't' },\n      'Dřevo': { price: 8500, unit: 'm3' },\n      'Zdivo nosné': { price: 1850, unit: 'm2' },\n      'Zdivo příčky': { price: 1250, unit: 'm2' },\n      'Beton základy': { price: 3500, unit: 'm3' },\n      'Beton stropy': { price: 3800, unit: 'm3' },\n      'Concrete': { price: 3200, unit: 'm3' },\n      'Steel': { price: 28500, unit: 't' },\n      'Glass': { price: 2500, unit: 'm2' },\n      'Wood': { price: 8500, unit: 'm3' }\n    },\n    elements: {\n      'Stěna': { price: 2200, unit: 'm2' },\n      'Okno': { price: 8500, unit: 'ks' },\n      'Dveře': { price: 6500, unit: 'ks' },\n      'Sloup': { price: 4500, unit: 'ks' },\n      'Wall': { price: 2200, unit: 'm2' },\n      'Window': { price: 8500, unit: 'ks' },\n      'Door': { price: 6500, unit: 'ks' },\n      'Slab': { price: 3800, unit: 'm3' },\n      'Column': { price: 4500, unit: 'ks' },\n      'Roof': { price: 2800, unit: 'm2' }\n    },\n    labor: {\n      'Zedník': { price: 350, unit: 'hod' },\n      'Tesař': { price: 380, unit: 'hod' },\n      'Elektrikář': { price: 420, unit: 'hod' },\n      'Instalatér': { price: 400, unit: 'hod' }\n    }\n  };\n}\n\n// Helper function to generate budget\nfunction generateBudget(extractedData, priceData) {\n  const items = [];\n  let totalPrice = 0;\n  \n  // Process materials\n  if (extractedData.metadata.materials) {\n    extractedData.metadata.materials.forEach(material => {\n      const priceInfo = priceData.materials[material.name];\n      if (priceInfo) {\n        const itemPrice = material.quantity * priceInfo.price;\n        items.push({\n          category: 'Materiál',\n          name: material.name,\n          quantity: material.quantity,\n          unit: material.unit || priceInfo.unit,\n          unitPrice: priceInfo.price,\n          totalPrice: itemPrice\n        });\n        totalPrice += itemPrice;\n      }\n    });\n  }\n  \n  // Process elements\n  if (extractedData.metadata.elements) {\n    extractedData.metadata.elements.forEach(element => {\n      const priceInfo = priceData.elements[element.type];\n      if (priceInfo) {\n        const itemPrice = element.count * priceInfo.price;\n        items.push({\n          category: 'Konstrukce',\n          name: element.type,\n          quantity: element.count,\n          unit: priceInfo.unit,\n          unitPrice: priceInfo.price,\n          totalPrice: itemPrice\n        });\n        totalPrice += itemPrice;\n      }\n    });\n  }\n  \n  // Add labor costs (estimated based on project size)\n  const projectSize = estimateProjectSize(extractedData);\n  const laborHours = {\n    'Zedník': projectSize * 120,\n    'Tesař': projectSize * 80,\n    'Elektrikář': projectSize * 60,\n    'Instalatér': projectSize * 50\n  };\n  \n  Object.entries(laborHours).forEach(([worker, hours]) => {\n    const priceInfo = priceData.labor[worker];\n    if (priceInfo) {\n      const itemPrice = hours * priceInfo.price;\n      items.push({\n        category: 'Práce',\n        name: worker,\n        quantity: hours,\n        unit: priceInfo.unit,\n        unitPrice: priceInfo.price,\n        totalPrice: itemPrice\n      });\n      totalPrice += itemPrice;\n    }\n  });\n  \n  // Add overhead and profit\n  const overhead = totalPrice * 0.15;\n  items.push({\n    category: 'Režie',\n    name: 'Režijní náklady',\n    quantity: 1,\n    unit: 'soubor',\n    unitPrice: overhead,\n    totalPrice: overhead\n  });\n  \n  const profit = totalPrice * 0.1;\n  items.push({\n    category: 'Zisk',\n    name: 'Zisk',\n    quantity: 1,\n    unit: 'soubor',\n    unitPrice: profit,\n    totalPrice: profit\n  });\n  \n  totalPrice += overhead + profit;\n  \n  return {\n    items,\n    totalPrice\n  };\n}\n\n// Helper function to estimate project size (0.5 - 5.0)\nfunction estimateProjectSize(extractedData) {\n  let size = 1.0; // Default medium size\n  \n  // Adjust based on building dimensions if available\n  if (extractedData.metadata.building) {\n    const building = extractedData.metadata.building;\n    if (building.grossArea) {\n      size = Math.max(0.5, Math.min(5.0, building.grossArea / 500));\n    } else if (building.storeys) {\n      size = Math.max(0.5, Math.min(5.0, building.storeys / 2));\n    }\n  } \n  // Adjust based on dimensions if available\n  else if (extractedData.metadata.dimensions) {\n    const dimensions = extractedData.metadata.dimensions;\n    if (dimensions.length && dimensions.width) {\n      const area = dimensions.length * dimensions.width;\n      size = Math.max(0.5, Math.min(5.0, area / 250));\n    }\n  }\n  // Adjust based on number of materials if available\n  else if (extractedData.metadata.materials && extractedData.metadata.materials.length > 0) {\n    size = Math.max(0.5, Math.min(5.0, extractedData.metadata.materials.length / 5));\n  }\n  \n  return size;\n}\n\n// Helper function to save budget\nasync function saveBudget(budget) {\n  // In a real implementation, this would call the API to save the budget\n  // For this template, we'll simulate API call\n  \n  try {\n    const response = await $http.post({\n      url: `${$env.BUILDING_BUDGET_API_URL}/api/budgets`,\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${$env.BUILDING_BUDGET_API_KEY}`\n      },\n      body: budget\n    });\n    \n    return response;\n  } catch (error) {\n    console.error('Error saving budget:', error);\n    // For demo purposes, return simulated response\n    return {\n      success: true,\n      data: {\n        ...budget,\n        _id: `budget_${Date.now()}`\n      }\n    };\n  }\n}\n\n// Helper function to generate PDF\nasync function generateBudgetPDF(budget) {\n  // In a real implementation, this would generate a PDF\n  // For this template, we'll simulate PDF generation\n  \n  try {\n    const response = await $http.post({\n      url: `${$env.BUILDING_BUDGET_API_URL}/api/budgets/${budget._id}/pdf`,\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${$env.BUILDING_BUDGET_API_KEY}`\n      }\n    });\n    \n    return response.data.pdfUrl;\n  } catch (error) {\n    console.error('Error generating PDF:', error);\n    // For demo purposes, return simulated URL\n    return `${$env.BUILDING_BUDGET_API_URL}/api/budgets/${budget._id}/pdf`;\n  }\n}"}, "name": "Generate Budget", "type": "n8n-nodes-base.function", "position": [250, 300]}, {"parameters": {"content": "={{ $json }}"}, "name": "Return Budget", "type": "n8n-nodes-base.respond", "position": [450, 300]}], "connections": {"Generate Budget": {"main": [[{"node": "Return Budget", "type": "main", "index": 0}]]}}}