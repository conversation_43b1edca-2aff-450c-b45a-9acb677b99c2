{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 20 3-3h2a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h2l3 3z\",\n  key: \"rbahqx\"\n}], [\"path\", {\n  d: \"M6 8v1\",\n  key: \"1636ez\"\n}], [\"path\", {\n  d: \"M10 8v1\",\n  key: \"1talb4\"\n}], [\"path\", {\n  d: \"M14 8v1\",\n  key: \"1rsfgr\"\n}], [\"path\", {\n  d: \"M18 8v1\",\n  key: \"gnkwox\"\n}]];\nconst EthernetPort = createLucideIcon(\"ethernet-port\", __iconNode);\nexport { __iconNode, EthernetPort as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "EthernetPort", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Plocha\\dating-app\\my-dating-app\\node_modules\\lucide-react\\src\\icons\\ethernet-port.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm15 20 3-3h2a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h2l3 3z',\n      key: 'rbahqx',\n    },\n  ],\n  ['path', { d: 'M6 8v1', key: '1636ez' }],\n  ['path', { d: 'M10 8v1', key: '1talb4' }],\n  ['path', { d: 'M14 8v1', key: '1rsfgr' }],\n  ['path', { d: 'M18 8v1', key: 'gnkwox' }],\n];\n\n/**\n * @component @name EthernetPort\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMjAgMy0zaDJhMiAyIDAgMCAwIDItMlY2YTIgMiAwIDAgMC0yLTJINGEyIDIgMCAwIDAtMiAydjlhMiAyIDAgMCAwIDIgMmgybDMgM3oiIC8+CiAgPHBhdGggZD0iTTYgOHYxIiAvPgogIDxwYXRoIGQ9Ik0xMCA4djEiIC8+CiAgPHBhdGggZD0iTTE0IDh2MSIgLz4KICA8cGF0aCBkPSJNMTggOHYxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ethernet-port\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst EthernetPort = createLucideIcon('ethernet-port', __iconNode);\n\nexport default EthernetPort;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAC,YAAA,GAAeC,gBAAiB,kBAAiBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}