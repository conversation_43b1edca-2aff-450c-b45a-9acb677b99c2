{"name": "IFC Extractor", "nodes": [{"parameters": {"functionCode": "// Input data should contain file information\nconst fileData = $input.item.json;\nconst binaryData = $input.item.binary;\n\n// Ensure we have the necessary data\nif (!fileData || !binaryData || !binaryData.data) {\n  throw new Error('Missing file data');\n}\n\n// Extract data from IFC\nconst extractedData = await extractDataFromIFC(binaryData.data);\n\n// Parse extracted data to identify elements, quantities, etc.\nconst metadata = parseExtractedData(extractedData);\n\n// Create extracted data record\nconst extractedDataRecord = {\n  fileId: fileData.fileId,\n  projectId: fileData.projectId,\n  fileType: 'ifc',\n  extractionDate: new Date().toISOString(),\n  rawData: JSON.stringify(extractedData),\n  metadata: metadata\n};\n\n// Save extracted data to database\nconst savedData = await saveExtractedData(extractedDataRecord);\n\n// Return the result\nreturn {\n  ...fileData,\n  extractedData: savedData.data,\n  extractedDataId: savedData.data._id\n};\n\n// Helper function to extract data from IFC\nasync function extractDataFromIFC(ifcBuffer) {\n  // In a real implementation, this would use an IFC parsing library\n  // For this template, we'll simulate extraction\n  return {\n    project: {\n      name: '<PERSON><PERSON><PERSON>ů<PERSON> Kv<PERSON>ná - BIM model',\n      description: 'Kompletní BIM model bytového domu',\n      units: 'meters'\n    },\n    building: {\n      storeys: 4,\n      grossArea: 1250,\n      netArea: 980,\n      height: 12.5\n    },\n    elements: [\n      { type: 'IfcWall', count: 120, volume: 185.5, material: 'Concrete' },\n      { type: 'IfcWindow', count: 48, area: 86.4, material: 'Glass' },\n      { type: 'IfcDoor', count: 35, area: 73.5, material: 'Wood' },\n      { type: 'IfcSlab', count: 8, volume: 125.0, material: 'Concrete' },\n      { type: 'IfcColumn', count: 24, volume: 12.8, material: 'Steel' },\n      { type: 'IfcRoof', count: 1, area: 320.0, material: 'Concrete' }\n    ],\n    spaces: [\n      { type: 'Apartment', count: 8, area: 720.0 },\n      { type: 'Common Area', count: 4, area: 180.0 },\n      { type: 'Technical Room', count: 2, area: 80.0 }\n    ],\n    materials: [\n      { name: 'Concrete', volume: 310.5 },\n      { name: 'Steel', volume: 12.8 },\n      { name: 'Glass', area: 86.4 },\n      { name: 'Wood', area: 73.5 }\n    ]\n  };\n}\n\n// Helper function to parse extracted data\nfunction parseExtractedData(data) {\n  // In a real implementation, this would analyze the IFC elements\n  // For this template, we'll simulate parsing\n  \n  const elements = [];\n  data.elements.forEach(element => {\n    elements.push({\n      type: element.type.replace('Ifc', ''),\n      count: element.count,\n      volume: element.volume,\n      area: element.area,\n      material: element.material\n    });\n  });\n  \n  const materials = [];\n  data.materials.forEach(material => {\n    materials.push({\n      name: material.name,\n      quantity: material.volume || material.area,\n      unit: material.volume ? 'm3' : 'm2'\n    });\n  });\n  \n  const spaces = [];\n  data.spaces.forEach(space => {\n    spaces.push({\n      name: space.type,\n      count: space.count,\n      area: space.area\n    });\n  });\n  \n  return {\n    projectName: data.project.name,\n    elements,\n    materials,\n    spaces,\n    building: data.building\n  };\n}\n\n// Helper function to save extracted data\nasync function saveExtractedData(data) {\n  // In a real implementation, this would call the API to save the data\n  // For this template, we'll simulate API call\n  \n  try {\n    const response = await $http.post({\n      url: `${$env.BUILDING_BUDGET_API_URL}/api/extracted-data`,\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${$env.BUILDING_BUDGET_API_KEY}`\n      },\n      body: data\n    });\n    \n    return response;\n  } catch (error) {\n    console.error('Error saving extracted data:', error);\n    // For demo purposes, return simulated response\n    return {\n      success: true,\n      data: {\n        ...data,\n        _id: `ifc_${Date.now()}`\n      }\n    };\n  }\n}"}, "name": "Extract IFC Data", "type": "n8n-nodes-base.function", "position": [250, 300]}, {"parameters": {"content": "={{ $json }}"}, "name": "Return Extracted Data", "type": "n8n-nodes-base.respond", "position": [450, 300]}], "connections": {"Extract IFC Data": {"main": [[{"node": "Return Extracted Data", "type": "main", "index": 0}]]}}}