{"name": "PDF Extractor", "nodes": [{"parameters": {"functionCode": "// Input data should contain file information\nconst fileData = $input.item.json;\nconst binaryData = $input.item.binary;\n\n// Ensure we have the necessary data\nif (!fileData || !binaryData || !binaryData.data) {\n  throw new Error('Missing file data');\n}\n\n// Extract text from PDF\nconst extractedText = await extractTextFromPDF(binaryData.data);\n\n// Parse extracted text to identify materials, dimensions, etc.\nconst metadata = parseExtractedText(extractedText);\n\n// Create extracted data record\nconst extractedData = {\n  fileId: fileData.fileId,\n  projectId: fileData.projectId,\n  fileType: 'pdf',\n  extractionDate: new Date().toISOString(),\n  rawText: extractedText,\n  metadata: metadata\n};\n\n// Save extracted data to database\nconst savedData = await saveExtractedData(extractedData);\n\n// Return the result\nreturn {\n  ...fileData,\n  extractedData: savedData.data,\n  extractedDataId: savedData.data._id\n};\n\n// Helper function to extract text from PDF\nasync function extractTextFromPDF(pdfBuffer) {\n  // In a real implementation, this would use a PDF parsing library\n  // For this template, we'll simulate extraction\n  return `\n    TECHNICKÁ ZPRÁVA\n    \n    Projekt: Bytový dům Květná\n    \n    Materiály:\n    - Beton: 120 m3\n    - Cihly: 8500 ks\n    - Ocel: 2.5 t\n    - Dřevo: 15 m3\n    \n    Rozměry:\n    - Délka: 25 m\n    - Šířka: 15 m\n    - Výška: 12 m\n    \n    Místnosti:\n    - Obývací pokoje: 10\n    - Koupelny: 8\n    - Kuchyně: 5\n    \n    Konstrukce:\n    - Základy: železobeton\n    - Stěny: cihelné zdivo\n    - Stropy: železobeton\n    - Střecha: sedlová\n  `;\n}\n\n// Helper function to parse extracted text\nfunction parseExtractedText(text) {\n  // In a real implementation, this would use NLP or regex to extract structured data\n  // For this template, we'll simulate parsing\n  \n  const materials = [\n    { name: 'Beton', quantity: 120, unit: 'm3' },\n    { name: 'Cihly', quantity: 8500, unit: 'ks' },\n    { name: 'Ocel', quantity: 2.5, unit: 't' },\n    { name: 'Dřevo', quantity: 15, unit: 'm3' }\n  ];\n  \n  const dimensions = {\n    length: 25,\n    width: 15,\n    height: 12,\n    unit: 'm'\n  };\n  \n  const rooms = {\n    livingRooms: 10,\n    bathrooms: 8,\n    kitchens: 5\n  };\n  \n  const construction = [\n    { type: 'Základy', material: 'železobeton' },\n    { type: 'Stěny', material: 'cihelné zdivo' },\n    { type: 'Stropy', material: 'železobeton' },\n    { type: 'Střecha', material: 'sedlová' }\n  ];\n  \n  return {\n    projectName: 'Bytový dům Květná',\n    materials,\n    dimensions,\n    rooms,\n    construction\n  };\n}\n\n// Helper function to save extracted data\nasync function saveExtractedData(data) {\n  // In a real implementation, this would call the API to save the data\n  // For this template, we'll simulate API call\n  \n  try {\n    const response = await $http.post({\n      url: `${$env.BUILDING_BUDGET_API_URL}/api/extracted-data`,\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${$env.BUILDING_BUDGET_API_KEY}`\n      },\n      body: data\n    });\n    \n    return response;\n  } catch (error) {\n    console.error('Error saving extracted data:', error);\n    // For demo purposes, return simulated response\n    return {\n      success: true,\n      data: {\n        ...data,\n        _id: `pdf_${Date.now()}`\n      }\n    };\n  }\n}"}, "name": "Extract PDF Data", "type": "n8n-nodes-base.function", "position": [250, 300]}, {"parameters": {"content": "={{ $json }}"}, "name": "Return Extracted Data", "type": "n8n-nodes-base.respond", "position": [450, 300]}], "connections": {"Extract PDF Data": {"main": [[{"node": "Return Extracted Data", "type": "main", "index": 0}]]}}}